package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivablePageDto;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivableStoreDetailsDto;
import com.jygjexp.jynx.tms.dto.TmsFinanceReceivableStorePageDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.service.TmsFinanceReceivableService;
import com.jygjexp.jynx.tms.service.TmsStoreOrderService;
import com.jygjexp.jynx.tms.vo.TmsFinanceReceivablePageVo;
import com.jygjexp.jynx.tms.vo.TmsFinanceReceivableStorePageVo;
import com.mongoplus.toolkit.CollUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * @Description
 * @Date 2025/8/5 11:37
 * @Created guqingren
 */

@Service
@RequiredArgsConstructor
public class TmsFinanceReceivableServiceImpl implements TmsFinanceReceivableService {

    private final TmsStoreOrderService tmsStoreOrderService;

    /**
     * 分页
     *
     * @param vo
     * @return
     */
    @Override
    public Page<TmsFinanceReceivablePageDto> search(TmsFinanceReceivablePageVo vo) {
        MPJLambdaWrapper<TmsStoreOrderEntity> wrapper = JoinWrappers.lambda(TmsStoreOrderEntity.class)
                .selectCount(TmsStoreOrderEntity::getId, TmsFinanceReceivablePageDto::getNumberOfOrders)
                .selectSum(TmsStoreOrderEntity::getTotalFreightAmount, TmsFinanceReceivablePageDto::getTotalFreightAmount)
                .selectSum(TmsStoreOrderEntity::getPayAmount, TmsFinanceReceivablePageDto::getPayAmount)
                .selectFunc("DATE_FORMAT(%s, '%%Y-%%m')",
                        arg -> arg.accept(TmsStoreOrderEntity::getCreateTime), TmsFinanceReceivablePageDto::getBillingCycle)
                .selectFunc("SUM(%s) - SUM(%s)",
                        arg -> arg.accept(TmsStoreOrderEntity::getTotalFreightAmount, TmsStoreProviderRelationEntity::getBaseFreightAmount)
                        , TmsFinanceReceivablePageDto::getPriceDifference)
                .selectAs(TmsStoreEntity::getId, TmsFinanceReceivablePageDto::getStoreId)
                .selectAs(TmsStoreEntity::getStoreName, TmsFinanceReceivablePageDto::getStoreName)
                .selectAs(TmsStoreEntity::getStoreType, TmsFinanceReceivablePageDto::getStoreType)
                .leftJoin(TmsStoreEntity.class, TmsStoreEntity::getId, TmsStoreOrderEntity::getStoreId)
                .leftJoin(TmsStoreProviderRelationEntity.class, TmsStoreProviderRelationEntity::getMainEntrustedOrder, TmsStoreOrderEntity::getEntrustedOrderNumber)
                .eq(TmsStoreOrderEntity::getDelFlag, 0)
                .eq(TmsStoreOrderEntity::getSubFlag, 0)
                .eq(TmsStoreOrderEntity::getWriteOffFlag, 1)
                .isNotNull(TmsStoreOrderEntity::getStoreId)
                .eq(ObjUtil.isNotNull(vo.getStoreId()), TmsStoreOrderEntity::getStoreId, vo.getStoreId())
                .groupBy(TmsStoreEntity::getId, TmsStoreEntity::getStoreName, TmsStoreEntity::getStoreType)
                .groupBy("billingCycle")
                .orderByDesc(TmsStoreOrderEntity::getCreateTime)
                .logicDelToOn();

        if (ObjUtil.isNotNull(vo.getBillingCycle())) {
            LocalDate billingCycle = vo.getBillingCycle();
            wrapper.between(TmsStoreOrderEntity::getCreateTime,
                    billingCycle.with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0),
                    billingCycle.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59));
        }
        return tmsStoreOrderService.selectJoinListPage(new Page<>(vo.getCurrent(), vo.getSize()), TmsFinanceReceivablePageDto.class, wrapper);
    }

    /**
     * 详情
     *
     * @param storeId
     * @return
     */
    @Override
    public TmsFinanceReceivablePageDto details(Long storeId) {
        MPJLambdaWrapper<TmsStoreOrderEntity> wrapper = JoinWrappers.lambda(TmsStoreOrderEntity.class)
                .selectCount(TmsStoreOrderEntity::getId, TmsFinanceReceivablePageDto::getNumberOfOrders)
                .selectSum(TmsStoreOrderEntity::getTotalFreightAmount, TmsFinanceReceivablePageDto::getTotalFreightAmount)
                .selectSum(TmsStoreOrderEntity::getPayAmount, TmsFinanceReceivablePageDto::getPayAmount)
                .selectFunc("DATE_FORMAT(%s, '%%Y-%%m')",
                        arg -> arg.accept(TmsStoreOrderEntity::getCreateTime), TmsFinanceReceivablePageDto::getBillingCycle)
                .selectFunc("SUM(%s) - SUM(%s)",
                        arg -> arg.accept(TmsStoreOrderEntity::getTotalFreightAmount, TmsStoreProviderRelationEntity::getBaseFreightAmount)
                        , TmsFinanceReceivablePageDto::getPriceDifference)
                .selectAs(TmsStoreEntity::getId, TmsFinanceReceivablePageDto::getStoreId)
                .selectAs(TmsStoreEntity::getStoreName, TmsFinanceReceivablePageDto::getStoreName)
                .selectAs(TmsStoreEntity::getStoreType, TmsFinanceReceivablePageDto::getStoreType)
                .leftJoin(TmsStoreEntity.class, TmsStoreEntity::getId, TmsStoreOrderEntity::getStoreId)
                .leftJoin(TmsStoreProviderRelationEntity.class, TmsStoreProviderRelationEntity::getMainEntrustedOrder, TmsStoreOrderEntity::getEntrustedOrderNumber)
                .eq(TmsStoreOrderEntity::getDelFlag, 0)
                .eq(TmsStoreOrderEntity::getSubFlag, 0)
                .eq(TmsStoreOrderEntity::getWriteOffFlag, 1)
                .eq(TmsStoreOrderEntity::getStoreId, storeId)
                .groupBy(TmsStoreEntity::getId, TmsStoreEntity::getStoreName, TmsStoreEntity::getStoreType)
                .groupBy("billingCycle")
                .logicDelToOn();
        TmsFinanceReceivablePageDto dto = tmsStoreOrderService.selectJoinOne(TmsFinanceReceivablePageDto.class, wrapper);
        return dto;
    }

    /**
     * 管理端-详情分页
     *
     * @param storeId
     * @param current
     * @param size
     * @return
     */
    @Override
    public Page<TmsFinanceReceivableStorePageDto> detailsPage(Long storeId, Integer current, Integer size) {
        MPJLambdaWrapper<TmsStoreOrderEntity> wrapper = JoinWrappers.lambda(TmsStoreOrderEntity.class)
                .selectAs(TmsStoreOrderEntity::getId, TmsFinanceReceivableStorePageDto::getOrderId)
                .selectAs(TmsStoreOrderEntity::getCreateTime, TmsFinanceReceivableStorePageDto::getOrderCreateTime)
                .selectAs(TmsStoreOrderEntity::getTotalFreightAmount, TmsFinanceReceivableStorePageDto::getTotalFreightAmount)
                .selectAs(TmsStoreOrderEntity::getPayAmount, TmsFinanceReceivableStorePageDto::getPayAmount)
                .selectAsClass(TmsStoreOrderEntity.class, TmsFinanceReceivableStorePageDto.class)
                .selectAsClass(TmsStoreCustomerEntity.class, TmsFinanceReceivableStorePageDto.class)
                .leftJoin(TmsStoreCustomerEntity.class, TmsStoreCustomerEntity::getId, TmsStoreOrderEntity::getStoreCustomerId)
                .eq(TmsStoreOrderEntity::getDelFlag, 0)
                .eq(TmsStoreOrderEntity::getSubFlag, 0)
                .eq(TmsStoreOrderEntity::getWriteOffFlag, 1)
                .eq(TmsStoreOrderEntity::getStoreId, storeId)
                .logicDelToOn();
        return tmsStoreOrderService.selectJoinListPage(new Page<>(current, size), TmsFinanceReceivableStorePageDto.class, wrapper);
    }

    /**
     * 管理端-导出
     *
     * @param vo
     * @return
     */
    @Override
    public List<TmsFinanceReceivablePageDto> export(TmsFinanceReceivablePageVo vo) {
        MPJLambdaWrapper<TmsStoreOrderEntity> wrapper = JoinWrappers.lambda(TmsStoreOrderEntity.class)
                .selectCount(TmsStoreOrderEntity::getId, TmsFinanceReceivablePageDto::getNumberOfOrders)
                .selectSum(TmsStoreOrderEntity::getTotalFreightAmount, TmsFinanceReceivablePageDto::getTotalFreightAmount)
                .selectSum(TmsStoreOrderEntity::getPayAmount, TmsFinanceReceivablePageDto::getPayAmount)
                .selectFunc("DATE_FORMAT(%s, '%%Y-%%m')",
                        arg -> arg.accept(TmsStoreOrderEntity::getCreateTime), TmsFinanceReceivablePageDto::getBillingCycle)
                .selectAs(TmsStoreEntity::getId, TmsFinanceReceivablePageDto::getStoreId)
                .selectAs(TmsStoreEntity::getStoreName, TmsFinanceReceivablePageDto::getStoreName)
                .selectAs(TmsStoreEntity::getStoreType, TmsFinanceReceivablePageDto::getStoreType)
                .leftJoin(TmsStoreEntity.class, TmsStoreEntity::getId, TmsStoreOrderEntity::getStoreId)
                .groupBy(TmsStoreEntity::getId, TmsStoreEntity::getStoreName, TmsStoreEntity::getStoreType)
                .groupBy("billingCycle")
                .eq(ObjUtil.isNotNull(vo.getStoreId()), TmsStoreOrderEntity::getStoreId, vo.getStoreId())
                .eq(TmsStoreOrderEntity::getSubFlag, 0)
                .eq(TmsStoreOrderEntity::getDelFlag, 0)
                .eq(TmsStoreOrderEntity::getWriteOffFlag, 1)
                .isNotNull(TmsStoreOrderEntity::getStoreId)
                .in(CollUtil.isNotEmpty(vo.getStoreIds()), TmsStoreOrderEntity::getStoreId, vo.getStoreIds())
                .orderByDesc(TmsStoreOrderEntity::getCreateTime)
                .logicDelToOn();

        if (ObjUtil.isNotNull(vo.getBillingCycle())) {
            LocalDate billingCycle = vo.getBillingCycle();
            wrapper.between(TmsStoreOrderEntity::getCreateTime,
                    billingCycle.with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0),
                    billingCycle.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59));
        }
        return tmsStoreOrderService.selectJoinListPage(new Page<>(vo.getCurrent(), vo.getSize()), TmsFinanceReceivablePageDto.class, wrapper)
                .getRecords();
    }

    /**
     * 门店端-分页
     *
     * @param vo
     * @return
     */
    @Override
    public Page<TmsFinanceReceivableStorePageDto> storeSearch(TmsFinanceReceivableStorePageVo vo) {
        MPJLambdaWrapper<TmsStoreOrderEntity> wrapper = JoinWrappers.lambda(TmsStoreOrderEntity.class)
                .selectAs(TmsStoreOrderEntity::getId, TmsFinanceReceivableStorePageDto::getOrderId)
                .selectAs(TmsStoreOrderEntity::getCreateTime, TmsFinanceReceivableStorePageDto::getOrderCreateTime)
                .selectAs(TmsStoreOrderEntity::getTotalFreightAmount, TmsFinanceReceivableStorePageDto::getTotalFreightAmount)
                .selectAs(TmsStoreOrderEntity::getPayAmount, TmsFinanceReceivableStorePageDto::getPayAmount)
                .selectAs(TmsStoreOrderEntity::getPayType, TmsFinanceReceivableStorePageDto::getPayType)
                .selectAsClass(TmsStoreOrderEntity.class, TmsFinanceReceivableStorePageDto.class)
                .selectAsClass(TmsStoreCustomerEntity.class, TmsFinanceReceivableStorePageDto.class)
                .leftJoin(TmsStoreCustomerEntity.class, TmsStoreCustomerEntity::getId, TmsStoreOrderEntity::getStoreCustomerId)
                .eq(ObjUtil.isNotNull(vo.getStoreCustomerId()), TmsStoreOrderEntity::getStoreCustomerId, vo.getStoreCustomerId())
                .eq(ObjUtil.isNotNull(vo.getStoreId()), TmsStoreOrderEntity::getStoreId, vo.getStoreId())
                .eq(TmsStoreOrderEntity::getSubFlag, 0)
                .eq(TmsStoreOrderEntity::getDelFlag, 0)
                .eq(TmsStoreOrderEntity::getWriteOffFlag, 1)
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNumber()), TmsStoreOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                .like(StrUtil.isNotBlank(vo.getExternalOrderNumber()), TmsStoreOrderEntity::getExternalOrderNumber, vo.getExternalOrderNumber())
                .isNotNull(TmsStoreOrderEntity::getStoreId)
                .between(ObjUtil.isNotNull(vo.getOrderCreateStartTime()) && ObjUtil.isNotNull(vo.getOrderCreateEndTime()),
                        TmsStoreOrderEntity::getCreateTime, vo.getOrderCreateStartTime(), vo.getOrderCreateEndTime())
                .orderByDesc(TmsStoreOrderEntity::getCreateTime)
                .logicDelToOn();
        return tmsStoreOrderService.selectJoinListPage(new Page<>(vo.getCurrent(), vo.getSize()), TmsFinanceReceivableStorePageDto.class, wrapper);
    }

    /**
     * 门店端-详情
     *
     * @param orderId
     * @return
     */
    @Override
    public TmsFinanceReceivableStoreDetailsDto storeDetails(Long orderId) {
        MPJLambdaWrapper<TmsStoreOrderEntity> wrapper = JoinWrappers.lambda(TmsStoreOrderEntity.class)
                .selectAs(TmsStoreOrderEntity::getId, TmsFinanceReceivableStoreDetailsDto::getOrderId)
                .selectAs(TmsStoreOrderEntity::getCreateTime, TmsFinanceReceivableStoreDetailsDto::getOrderCreateTime)
                .selectAs(TmsStoreProviderRelationEntity::getProviderName, TmsFinanceReceivableStoreDetailsDto::getServiceProvider)
                .selectAs(TmsStoreOrderEntity::getTotalFreightAmount, TmsFinanceReceivableStoreDetailsDto::getTotalFreightAmount)
                .selectAs(TmsStoreOrderEntity::getPayAmount, TmsFinanceReceivableStoreDetailsDto::getPayAmount)
                .selectAs(TmsStoreOrderEntity::getPayType, TmsFinanceReceivableStoreDetailsDto::getPayType)
                .selectCount(TmsStoreOrderGoodsEntity::getId, TmsFinanceReceivableStoreDetailsDto::getNumberOfPackages)
                .selectSum(TmsStoreOrderGoodsEntity::getWeight, TmsFinanceReceivableStoreDetailsDto::getWeight)
                .selectAsClass(TmsStoreOrderEntity.class, TmsFinanceReceivableStorePageDto.class)
                .selectAsClass(TmsStoreCustomerEntity.class, TmsFinanceReceivableStorePageDto.class)
                .selectFunc("sum(%s * %s * %s) / 1000000", arg -> arg.accept(TmsStoreOrderGoodsEntity::getLength,
                                TmsStoreOrderGoodsEntity::getWidth, TmsStoreOrderGoodsEntity::getHeight)
                        , TmsFinanceReceivableStoreDetailsDto::getVolume)
                .leftJoin(TmsStoreOrderGoodsEntity.class, TmsStoreOrderGoodsEntity::getMainEntrustedOrder, TmsStoreOrderEntity::getEntrustedOrderNumber)
                .leftJoin(TmsStoreCustomerEntity.class, TmsStoreCustomerEntity::getId, TmsStoreOrderEntity::getStoreCustomerId)
                .leftJoin(TmsStoreProviderRelationEntity.class, TmsStoreProviderRelationEntity::getMainEntrustedOrder, TmsStoreOrderEntity::getEntrustedOrderNumber)
                .eq(TmsStoreOrderEntity::getId, orderId)
                .eq(TmsStoreOrderEntity::getDelFlag, 0)
                .eq(TmsStoreOrderEntity::getSubFlag, 0)
                .eq(TmsStoreOrderEntity::getWriteOffFlag, 1)
                .isNotNull(TmsStoreOrderEntity::getStoreId)
                .groupBy(TmsStoreOrderEntity::getEntrustedOrderNumber, TmsStoreOrderEntity::getTotalFreightAmount, TmsStoreOrderEntity::getCreateTime)
                .groupBy(TmsStoreCustomerEntity::getName, TmsStoreCustomerEntity::getType, TmsStoreCustomerEntity::getLevel)
                .groupBy(TmsStoreProviderRelationEntity::getProviderName)
                .logicDelToOn();
        return tmsStoreOrderService.selectJoinOne(TmsFinanceReceivableStoreDetailsDto.class, wrapper);
    }

    /**
     * 门店端-导出
     *
     * @param vo
     * @return
     */
    @Override
    public List<TmsFinanceReceivableStorePageDto> storeExport(TmsFinanceReceivableStorePageVo vo) {
        MPJLambdaWrapper<TmsStoreOrderEntity> wrapper = JoinWrappers.lambda(TmsStoreOrderEntity.class)
                .selectAs(TmsStoreOrderEntity::getId, TmsFinanceReceivableStorePageDto::getOrderId)
                .selectAs(TmsStoreOrderEntity::getCreateTime, TmsFinanceReceivableStorePageDto::getOrderCreateTime)
                .selectAs(TmsStoreOrderEntity::getTotalFreightAmount, TmsFinanceReceivableStorePageDto::getTotalFreightAmount)
                .selectAs(TmsStoreOrderEntity::getPayAmount, TmsFinanceReceivableStorePageDto::getPayAmount)
                .selectAs(TmsStoreOrderEntity::getPayType, TmsFinanceReceivableStorePageDto::getPayType)
                .selectAsClass(TmsStoreOrderEntity.class, TmsFinanceReceivableStorePageDto.class)
                .selectAsClass(TmsStoreCustomerEntity.class, TmsFinanceReceivableStorePageDto.class)
                .leftJoin(TmsStoreCustomerEntity.class, TmsStoreCustomerEntity::getId, TmsStoreOrderEntity::getStoreCustomerId)
                .eq(ObjUtil.isNotNull(vo.getStoreId()), TmsStoreOrderEntity::getStoreId, vo.getStoreId())
                .eq(TmsStoreOrderEntity::getSubFlag, 0)
                .eq(TmsStoreOrderEntity::getDelFlag, 0)
                .eq(TmsStoreOrderEntity::getWriteOffFlag, 1)
                .eq(ObjUtil.isNotNull(vo.getStoreCustomerId()), TmsStoreOrderEntity::getStoreCustomerId, vo.getStoreCustomerId())
                .in(CollUtil.isNotEmpty(vo.getOrderIds()), TmsStoreOrderEntity::getId, vo.getOrderIds())
                .like(StrUtil.isNotBlank(vo.getEntrustedOrderNumber()), TmsStoreOrderEntity::getEntrustedOrderNumber, vo.getEntrustedOrderNumber())
                .isNotNull(TmsStoreOrderEntity::getStoreId)
                .between(ObjUtil.isNotNull(vo.getOrderCreateStartTime()) && ObjUtil.isNotNull(vo.getOrderCreateEndTime()),
                        TmsStoreOrderEntity::getCreateTime, vo.getOrderCreateStartTime(), vo.getOrderCreateEndTime())
                .orderByDesc(TmsStoreOrderEntity::getCreateTime)
                .logicDelToOn();
        return tmsStoreOrderService.selectJoinListPage(new Page<>(vo.getCurrent(), vo.getSize()), TmsFinanceReceivableStorePageDto.class, wrapper)
                .getRecords();
    }
}
