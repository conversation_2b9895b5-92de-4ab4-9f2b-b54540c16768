package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsPostEntity;
import com.jygjexp.jynx.tms.entity.TmsRegionEntity;
import com.jygjexp.jynx.tms.entity.TmsSiteEntity;
import com.jygjexp.jynx.tms.mapper.PostMapper;
import com.jygjexp.jynx.tms.mapper.TmsPostalCodeMapper;
import com.jygjexp.jynx.tms.mapper.TmsSiteMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsRoutePlanService;
import com.jygjexp.jynx.tms.service.TmsSiteService;
import com.jygjexp.jynx.tms.vo.TmsSitePageVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 卡派站点
 *
 * <AUTHOR>
 * @date 2025-03-11 14:37:38
 */
@Service
@RequiredArgsConstructor
public class TmsSiteServiceImpl extends ServiceImpl<TmsSiteMapper, TmsSiteEntity> implements TmsSiteService {
    private final TmsSiteMapper tmsSiteMapper;
    private final TmsRoutePlanService routePlanService;
    private final PostMapper tmsPostMapper;

    /**
     * 站点分页搜索框
     * @param page
     * @param tmsSite
     * @return
     */
    @Override
    public Page search(Page page, TmsSitePageVo tmsSite) {
        MPJLambdaWrapper<TmsSiteEntity> wrapper = new MPJLambdaWrapper<>();

        // 查询条件（site 全部类型，不限制是否为一级）
        wrapper.like(StrUtil.isNotBlank(tmsSite.getSiteName()), TmsSiteEntity::getSiteName, tmsSite.getSiteName())
                .eq(StrUtil.isNotBlank(tmsSite.getCityName()), TmsSiteEntity::getCityName, tmsSite.getCityName())
                .eq(StrUtil.isNotBlank(tmsSite.getSiteCode()), TmsSiteEntity::getSiteCode, tmsSite.getSiteCode())
                .eq(ObjectUtil.isNotNull(tmsSite.getSiteType()), TmsSiteEntity::getSiteType, tmsSite.getSiteType())
                .leftJoin(TmsRegionEntity.class, TmsRegionEntity::getRegionId, TmsSiteEntity::getRegionId)
                .selectAll(TmsSiteEntity.class)
                .selectAs(TmsRegionEntity::getRegionName, TmsSitePageVo.Fields.regionName)
                .orderByDesc(TmsSiteEntity::getId);

        // 正常分页查询 site
        Page<TmsSiteEntity> result = tmsSiteMapper.selectJoinPage(page, TmsSiteEntity.class, wrapper);
        List<TmsSiteEntity> siteList = result.getRecords();

        // 获取所有 site.id，查找 post 表中 parentWarehouseId 是这些 ID 的数据（即：三级仓）
        List<Long> siteIds = siteList.stream().map(TmsSiteEntity::getId).collect(Collectors.toList());

        if (!siteIds.isEmpty()) {
            List<TmsPostEntity> thirdPosts = tmsPostMapper.selectList(
                    new LambdaQueryWrapper<TmsPostEntity>().in(TmsPostEntity::getParentWarehouseId, siteIds)
            );

            // 分组：parentWarehouseId -> List<Post>
            Map<Long, List<TmsPostEntity>> thirdMap = thirdPosts.stream()
                    .collect(Collectors.groupingBy(TmsPostEntity::getParentWarehouseId));

            // 挂载：给每个对应的二级仓 site 挂上 postList
            for (TmsSiteEntity site : siteList) {
                site.setPostList(thirdMap.getOrDefault(site.getId(), new ArrayList<>()));
            }
        }

        return result;
    }
//    @Override
//    public Page search(Page page, TmsSitePageVo tmsSite) {
//        MPJLambdaWrapper<TmsSiteEntity> wrapper = new MPJLambdaWrapper<>();
//
//        // 只查一级仓（parentWarehouseId 为 null）
//        wrapper.eq(TmsSiteEntity::getParentWarehouseId, 0);
//
//        // 其他查询条件（可选过滤）
//        wrapper.like(StrUtil.isNotBlank(tmsSite.getSiteName()), TmsSiteEntity::getSiteName, tmsSite.getSiteName())
//                .eq(StrUtil.isNotBlank(tmsSite.getCityName()), TmsSiteEntity::getCityName, tmsSite.getCityName())
//                .eq(StrUtil.isNotBlank(tmsSite.getSiteCode()), TmsSiteEntity::getSiteCode, tmsSite.getSiteCode())
//                .eq(ObjectUtil.isNotNull(tmsSite.getSiteType()), TmsSiteEntity::getSiteType, tmsSite.getSiteType())
//                .leftJoin(TmsRegionEntity.class, TmsRegionEntity::getRegionId, TmsSiteEntity::getRegionId)
//                .selectAll(TmsSiteEntity.class)
//                .selectAs(TmsRegionEntity::getRegionName, TmsSitePageVo.Fields.regionName)
//                .orderByDesc(TmsSiteEntity::getId);
//
//        // 分页查一级仓
//        Page<TmsSiteEntity> result = tmsSiteMapper.selectJoinPage(page, TmsSiteEntity.class, wrapper);
//        List<TmsSiteEntity> topSites = result.getRecords();
//
//        // 批量查询所有二级仓和三级仓，避免N+1查询
//        List<Long> topSiteIds = topSites.stream().map(TmsSiteEntity::getId).collect(Collectors.toList());
//        if (!topSiteIds.isEmpty()) {
//            List<TmsSiteEntity> secondSites = tmsSiteMapper.selectList(
//                    new LambdaQueryWrapper<TmsSiteEntity>().in(TmsSiteEntity::getParentWarehouseId, topSiteIds)
//            );
//
//            // 获取所有二级仓 ID
//            List<Long> secondIds = secondSites.stream().map(TmsSiteEntity::getId).collect(Collectors.toList());
//            List<TmsPostEntity> thirdPosts = secondIds.isEmpty() ? new ArrayList<>() :
//                    tmsPostMapper.selectList(
//                            new LambdaQueryWrapper<TmsPostEntity>().in(TmsPostEntity::getParentWarehouseId, secondIds)
//                    );
//
//            // 将三级仓挂到对应二级仓上
//            Map<Long, List<TmsPostEntity>> thirdMap = thirdPosts.stream()
//                    .collect(Collectors.groupingBy(TmsPostEntity::getParentWarehouseId));
//            for (TmsSiteEntity second : secondSites) {
//                second.setPostList(thirdMap.getOrDefault(second.getId(), new ArrayList<>()));
//            }
//
//            // 将二级仓挂到对应一级仓上
//            Map<Long, List<TmsSiteEntity>> secondMap = secondSites.stream()
//                    .collect(Collectors.groupingBy(TmsSiteEntity::getParentWarehouseId));
//            for (TmsSiteEntity top : topSites) {
//                top.setChildren(secondMap.getOrDefault(top.getId(), new ArrayList<>()));
//            }
//        }
//
//        return result;
//    }

    /**
     * 保存站点
     * @param tmsSite
     * @return
     */
    @Override
    public R saveSite(TmsSiteEntity tmsSite) {
        //默认启用
        //tmsSite.setIsValid(1);
        // 1.校验站点名称及站点代码是否重复
        if (tmsSiteMapper.selectCount(new MPJLambdaWrapper<TmsSiteEntity>()
                .eq(TmsSiteEntity::getSiteCode, tmsSite.getSiteCode())) > 0) {
            return LocalizedR.failed("tms.site.name.code.exists", "");
        }
        // 2.校验站点地址是否重复
//        if (tmsSiteMapper.selectCount(new MPJLambdaWrapper<TmsSiteEntity>()
//                .eq(TmsSiteEntity::getSiteAddress, tmsSite.getSiteAddress())) > 0) {
//            return LocalizedR.failed("tms.site.address.exists", "");
//        }

        // 校验站点地址不为null
        if (tmsSite.getSiteLat() == null && StrUtil.isNotBlank(tmsSite.getSiteAddress()) ) {
            // 根据地址查询经纬度
            String destLatLng = routePlanService.getLatLngByAddress(tmsSite.getSiteAddress());
            if (StrUtil.isNotBlank(destLatLng)) {
                String[] split = destLatLng.split(",");
                tmsSite.setSiteLat(new BigDecimal(split[0]));
                tmsSite.setSiteLng(new BigDecimal(split[1]));
            }

        }
        return R.ok(this.save(tmsSite));
    }


    // 根据站点名称查询站点信息
    @Override
    public TmsSiteEntity getSiteByName(String siteName) {
        LambdaQueryWrapper<TmsSiteEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsSiteEntity::getSiteName, siteName)
                .last("limit 1");
        return tmsSiteMapper.selectOne(wrapper);
    }

}