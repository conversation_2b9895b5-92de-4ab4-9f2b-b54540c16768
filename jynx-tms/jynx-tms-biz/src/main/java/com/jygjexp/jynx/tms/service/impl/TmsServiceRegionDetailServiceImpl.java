package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsServiceQuotePriceEntity;
import com.jygjexp.jynx.tms.entity.TmsServiceRegionDetailEntity;
import com.jygjexp.jynx.tms.entity.TmsTransportTaskOrderEntity;
import com.jygjexp.jynx.tms.mapper.TmsServiceRegionDetailMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsServiceRegionDetailService;
import com.jygjexp.jynx.tms.vo.TmsServiceReginDetailPageVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceReginDetaiUnreachabllExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsServiceReginDetailExcelVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 服务商邮编配置子表（分区明细）
 *
 * <AUTHOR>
 * @date 2025-07-10 14:49:36
 */
@RequiredArgsConstructor
@Service
public class TmsServiceRegionDetailServiceImpl extends ServiceImpl<TmsServiceRegionDetailMapper, TmsServiceRegionDetailEntity> implements TmsServiceRegionDetailService {
    private final TmsServiceRegionDetailMapper tmsServiceRegionDetailMapper;
    // 分页查询
    @Override
    public Page<TmsServiceReginDetailPageVo> search(Page page, TmsServiceReginDetailPageVo vo) {
        LambdaQueryWrapper<TmsServiceRegionDetailEntity> wrapper = new LambdaQueryWrapper<TmsServiceRegionDetailEntity>()
                .like(StrUtil.isNotBlank(vo.getPostalName()),TmsServiceRegionDetailEntity::getPostalName,vo.getPostalName())                   // 邮编分区
                .like(StrUtil.isNotBlank(vo.getPostalCode()),TmsServiceRegionDetailEntity::getPostalCode,vo.getPostalCode())      // 不可达匹配邮编
                .eq(ObjectUtil.isNotNull(vo.getIsValid()),TmsServiceRegionDetailEntity::getIsValid,vo.getIsValid())
                .like(StrUtil.isNotBlank(vo.getCreateBy()),TmsServiceRegionDetailEntity::getCreateBy,vo.getCreateBy())
                .like(StrUtil.isNotBlank(vo.getShipperPostalCodeStart()),TmsServiceRegionDetailEntity::getShipperPostalCodeStart,vo.getShipperPostalCodeStart())         // 发件人起始邮编
                .like(StrUtil.isNotBlank(vo.getShipperPostalCodeEnd()),TmsServiceRegionDetailEntity::getShipperPostalCodeEnd,vo.getShipperPostalCodeEnd())         // 发件人结束邮编
                .like(StrUtil.isNotBlank(vo.getPostalCodeStart()),TmsServiceRegionDetailEntity::getPostalCodeStart,vo.getPostalCodeStart())         // 目的地起始邮编
                .like(StrUtil.isNotBlank(vo.getPostalCodeEnd()),TmsServiceRegionDetailEntity::getPostalCodeEnd,vo.getPostalCodeEnd())         // 目的地结束邮编
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),TmsServiceRegionDetailEntity::getCreateTime,vo.getStartTime(),vo.getEndTime())
                .eq(ObjectUtil.isNotNull(vo.getRegionId()),TmsServiceRegionDetailEntity::getRegionId,vo.getRegionId())
                .orderByDesc(TmsServiceRegionDetailEntity::getCreateTime);

/*        // 匹配邮编起始或截止字段，OR 关系
        if (StrUtil.isNotBlank(vo.getPostalCodeStart())) {
            String keyword = vo.getPostalCodeStart().trim();
            wrapper.and(w -> w
                    .like(TmsServiceRegionDetailEntity::getPostalCodeStart, keyword)
                    .or()
                    .like(TmsServiceRegionDetailEntity::getPostalCodeEnd, keyword)
                    .or()
                    .like(TmsServiceRegionDetailEntity::getShipperPostalCodeStart, keyword)
                    .or()
                    .like(TmsServiceRegionDetailEntity::getShipperPostalCodeEnd, keyword)
            );
        }*/
        return tmsServiceRegionDetailMapper.selectPage(page, wrapper);
    }

    // 导出可达邮编配置
    @Override
    public List<TmsServiceReginDetailExcelVo> reachableRegionExport(TmsServiceReginDetailPageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsServiceRegionDetailEntity> wrapper = new MPJLambdaWrapper<TmsServiceRegionDetailEntity>()
                .selectAll(TmsServiceRegionDetailEntity.class)
                .like(StrUtil.isNotBlank(vo.getPostalName()),TmsServiceRegionDetailEntity::getPostalName,vo.getPostalName())                   // 邮编分区
                .like(StrUtil.isNotBlank(vo.getPostalCode()),TmsServiceRegionDetailEntity::getPostalCode,vo.getPostalCode())      // 不可达匹配邮编
                .eq(ObjectUtil.isNotNull(vo.getIsValid()),TmsServiceRegionDetailEntity::getIsValid,vo.getIsValid())
                .like(StrUtil.isNotBlank(vo.getCreateBy()),TmsServiceRegionDetailEntity::getCreateBy,vo.getCreateBy())
                .like(StrUtil.isNotBlank(vo.getShipperPostalCodeStart()),TmsServiceRegionDetailEntity::getShipperPostalCodeStart,vo.getShipperPostalCodeStart())         // 发件人起始邮编
                .like(StrUtil.isNotBlank(vo.getShipperPostalCodeEnd()),TmsServiceRegionDetailEntity::getShipperPostalCodeEnd,vo.getShipperPostalCodeEnd())         // 发件人结束邮编
                .like(StrUtil.isNotBlank(vo.getPostalCodeStart()),TmsServiceRegionDetailEntity::getPostalCodeStart,vo.getPostalCodeStart())         // 目的地起始邮编
                .like(StrUtil.isNotBlank(vo.getPostalCodeEnd()),TmsServiceRegionDetailEntity::getPostalCodeEnd,vo.getPostalCodeEnd())         // 目的地结束邮编
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),TmsServiceRegionDetailEntity::getCreateTime,vo.getStartTime(),vo.getEndTime())
                .eq(ObjectUtil.isNotNull(vo.getRegionId()),TmsServiceRegionDetailEntity::getRegionId,vo.getRegionId())
                .in(ObjectUtil.isNotEmpty(ids) && ids.length > 0, TmsTransportTaskOrderEntity::getId, ids)
                .orderByDesc(TmsServiceRegionDetailEntity::getCreateTime);
        return tmsServiceRegionDetailMapper.selectJoinList(TmsServiceReginDetailExcelVo.class,wrapper);
    }

    // 导出不可达邮编配置
    @Override
    public List<TmsServiceReginDetaiUnreachabllExcelVo> unreachableRegionExport(TmsServiceReginDetailPageVo vo, Long[] ids) {
        MPJLambdaWrapper<TmsServiceRegionDetailEntity> wrapper = new MPJLambdaWrapper<TmsServiceRegionDetailEntity>()
                .selectAll(TmsServiceRegionDetailEntity.class)
                .like(StrUtil.isNotBlank(vo.getPostalName()),TmsServiceRegionDetailEntity::getPostalName,vo.getPostalName())                   // 邮编分区
                .like(StrUtil.isNotBlank(vo.getPostalCode()),TmsServiceRegionDetailEntity::getPostalCode,vo.getPostalCode())      // 不可达匹配邮编
                .eq(ObjectUtil.isNotNull(vo.getIsValid()),TmsServiceRegionDetailEntity::getIsValid,vo.getIsValid())
                .like(StrUtil.isNotBlank(vo.getCreateBy()),TmsServiceRegionDetailEntity::getCreateBy,vo.getCreateBy())
                .between(StrUtil.isNotBlank(vo.getStartTime()) && StrUtil.isNotBlank(vo.getEndTime()),TmsServiceRegionDetailEntity::getCreateTime,vo.getStartTime(),vo.getEndTime())
                .eq(ObjectUtil.isNotNull(vo.getRegionId()),TmsServiceRegionDetailEntity::getRegionId,vo.getRegionId())
                .in(ObjectUtil.isNotEmpty(ids) && ids.length > 0, TmsTransportTaskOrderEntity::getId, ids)
                .orderByDesc(TmsServiceRegionDetailEntity::getCreateTime);
        return tmsServiceRegionDetailMapper.selectJoinList(TmsServiceReginDetaiUnreachabllExcelVo.class,wrapper);
    }

    // 导入可达分区邮编配置
    @Override
    public R reachableRegionImport(MultipartFile file, Long regionId) {
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("order.upload.valid.file", Optional.ofNullable(null));
        }

        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            List<List<Object>> lines = reader.read(1, reader.getRowCount());

            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("order.upload.empty.data", Optional.ofNullable(null));
            }

            int maxRows = 10000;
            if (lines.size() > maxRows) {
                return LocalizedR.failed("tms.ServiceRegionDetail.file.too.many.rows", maxRows);
            }

            List<String> errorMessages = new ArrayList<>();
            List<TmsServiceRegionDetailEntity> weightList = new ArrayList<>();
            int batchSize = 500;
            int totalSuccessCount = 0;

            for (int i = 0; i < lines.size(); i++) {
                List<Object> line = lines.get(i);
                List<String> lineErrors = new ArrayList<>();

                String postalName = validateStringField(line, 0, "The postal code area cannot be left blank/邮编分区不能为空", i + 1, lineErrors);
                String shipperPostalCodeStart = validateStringField(line, 1, "The shipper starting postal code cannot be left blank/发件起始邮编不能为空", i + 1, lineErrors);
                String shipperPostalCodeEnd = validateStringField(line, 2, "The shipper ending postal code cannot be left blank/发件结束邮编不能为空", i + 1, lineErrors);
                String postalCodeStart = validateStringField(line, 3, "The starting postal code cannot be left blank/起始邮编不能为空", i + 1, lineErrors);
                String postalCodeEnd = validateStringField(line, 4, "The ending postal code cannot be left blank/结束邮编不能为空", i + 1, lineErrors);
                BigDecimal transitDays = validateBigDecimalField(line, 5, "The time limit cannot be left blank/时效不能为空", i + 1, lineErrors);
                String portName = validateStringField(line, 6, "The landing port cannot be left blank/落地口岸不能为空", i + 1, lineErrors);

                if (StrUtil.isNotBlank(shipperPostalCodeStart) && !isValidCanadianPostalCode(shipperPostalCodeStart)) {
                    lineErrors.add("Row " + (i + 1) + ": Invalid start shipper postal code format（邮编格式错误 例：V1M 2C9）: " + shipperPostalCodeStart);
                }
                if (StrUtil.isNotBlank(shipperPostalCodeEnd) && !isValidCanadianPostalCode(shipperPostalCodeEnd)) {
                    lineErrors.add("Row " + (i + 1) + ": Invalid end shipper postal code format（邮编格式错误 例：V1M 2C9）: " + shipperPostalCodeEnd);
                }
                if (StrUtil.isNotBlank(postalCodeStart) && !isValidCanadianPostalCode(postalCodeStart)) {
                    lineErrors.add("Row " + (i + 1) + ": Invalid start postal code format（邮编格式错误 例：V1M 2C9）: " + postalCodeStart);
                }
                if (StrUtil.isNotBlank(postalCodeEnd) && !isValidCanadianPostalCode(postalCodeEnd)) {
                    lineErrors.add("Row " + (i + 1) + ": Invalid end postal code format（邮编格式错误 例：V1M 2C9）: " + postalCodeEnd);
                }

                validateWeightField(line, 0, 1, 2, 3, 4, regionId,
                        "The same postal code was repeatedly added in the postal code area division/邮编分区重复添加相同邮编", i + 1, lineErrors);

                if (!lineErrors.isEmpty()) {
                    errorMessages.addAll(lineErrors);
                } else {
                    TmsServiceRegionDetailEntity weightDetail = new TmsServiceRegionDetailEntity();
                    weightDetail.setRegionId(regionId);
                    weightDetail.setPostalName(postalName);
                    weightDetail.setShipperPostalCodeStart(shipperPostalCodeStart);
                    weightDetail.setShipperPostalCodeEnd(shipperPostalCodeEnd);
                    weightDetail.setPostalCodeStart(postalCodeStart);
                    weightDetail.setPostalCodeEnd(postalCodeEnd);
                    weightDetail.setTransitDays(transitDays);
                    weightDetail.setPortName(portName);

                    weightList.add(weightDetail);

                    if (weightList.size() >= batchSize) {
                        totalSuccessCount += processWeights(weightList);
                        weightList.clear();
                    }
                }
            }

            if (!weightList.isEmpty()) {
                totalSuccessCount += processWeights(weightList);
                weightList.clear();
            }

            String successMessage = "Successfully processed: " + totalSuccessCount + " rows.";
            if (errorMessages.isEmpty()) {
                return LocalizedR.ok("tms.ServiceRegionDetail.note.Excel.file.processing.success", successMessage);
            } else {
                return LocalizedR.failed("tms.ServiceRegionDetail.file.processing.errors",
                        successMessage + "<br>Errors:<br>" + errorMessages.stream().limit(100).collect(Collectors.joining("<br>")));
            }

        } catch (IOException e) {
            log.error("导入分区邮编配置明细Excel文件处理异常", e);
            return LocalizedR.failed("tms.ServiceRegionDetail.Excel.file.processing.exception", e.getMessage());
        }
    }


    // 导入不可达分区邮编配置
    @Override
    public R unreachableRegionImport(MultipartFile file, Long regionId) {
        // 验证文件类型和空文件
        if (file == null || file.isEmpty() || !isExcelFile(file.getOriginalFilename())) {
            return LocalizedR.failed("order.upload.valid.file", Optional.ofNullable(null));
        }
        try (InputStream inputStream = file.getInputStream()) {
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            //List<List<Object>> lines = reader.read(1, reader.getRowCount());
            List<List<Object>> lines = reader.read(1, reader.getRowCount()); // 跳过表头，确保从第二行开始读取
            // 验证Excel数据是否为空
            if (lines == null || lines.isEmpty()) {
                return LocalizedR.failed("order.upload.empty.data", Optional.ofNullable(null));
            }

            // 10000条记录的限制
            int maxRows = 10000;
            if (lines.size() > maxRows) {
                return LocalizedR.failed("tms.ServiceRegionDetail.file.too.many.rows", maxRows);
            }

            // 创建集合来记录所有问题的字段
            List<String> errorMessages = new ArrayList<>();
            List<TmsServiceRegionDetailEntity> weightList = new ArrayList<>();
            for (int i = 0; i < lines.size(); i++) {
                List<Object> line = lines.get(i);

                // 记录每行错误集合
                List<String> lineErrors = new ArrayList<>();

                // 验证字段
                String postalName = validateStringField(line, 0, "The postal code area cannot be left blank/邮编分区不能为空", i + 1, lineErrors);
                String portName = validateStringField(line, 1, "The landing port cannot be left blank/落地口岸不能为空", i + 1, lineErrors);

                // 校验加拿大邮编格式
                if (StrUtil.isNotBlank(postalName) && !isValidCanadianPostalCode(postalName)) {
                    lineErrors.add("Row " + (i + 1) + ": Invalid start postal code format（邮编格式错误 例：V1M 2C9）: " + postalName);
                }

                //validateWeightField(line, 0,1,2,regionId,
                       // "The same postal code was repeatedly added in the postal code area division/邮编分区重复添加相同邮编", i + 1, lineErrors);

                // 如果当前行有错误，跳过该行
                if (!lineErrors.isEmpty()) {
                    // 将当前行的所有错误添加到全局错误信息列表中
                    errorMessages.addAll(lineErrors);
                } else {
                    //准备插入字段阶段--------------------------------------
                    TmsServiceRegionDetailEntity weightDetail = new TmsServiceRegionDetailEntity();
                    weightDetail.setRegionId(regionId);
                    weightDetail.setPostalCode(postalName);
                    weightDetail.setPortName(portName);
                    weightList.add(weightDetail);
                }

            }

            // 保存新增至数据库
            Integer weightCount = processWeights(weightList);
            //记录成功条数信息
            String successMessage = "Successfully processed: " + weightCount + " rows.";

            if (errorMessages.isEmpty()) {
                return LocalizedR.ok("tms.ServiceRegionDetail.note.Excel.file.processing.success", successMessage);
            } else {
                // 返回成功数量和错误信息
                return LocalizedR.failed("tms.ServiceRegionDetail.file.processing.errors", successMessage + "<br>Errors:<br>" + errorMessages);
            }
        } catch (IOException e) {
            log.error("导入分区邮编配置明细Excel文件处理异常", e);
            return LocalizedR.failed("tms.ServiceRegionDetail.Excel.file.processing.exception", e.getMessage());
        }
    }


    /**
     * 验证加拿大邮编格式是否正确（格式如 V1M 2C9，可带空格或不带空格）
     */
    private boolean isValidCanadianPostalCode(String postalCode) {
        if (StrUtil.isBlank(postalCode)) return false;
        // 正则：字母数字字母 可选空格 数字字母数字（忽略大小写）
        return postalCode.toUpperCase().matches("^[A-Z]\\d[A-Z]\\s?\\d[A-Z]\\d$");
    }

    // 校验文件是否是.xls或.xlsx格式
    private boolean isExcelFile(String fileName) {
        return fileName != null && (fileName.endsWith(".xls") || fileName.endsWith(".xlsx"));
    }

    //校验Integer类型字段是否为空
    private Integer validateIntegerField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：" + value + "(format error)");
            return null;
        }
    }

    //校验String类型字段是否为空
    private String validateStringField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null || value.toString().trim().isEmpty()) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    //校验Decimal类型字段是否为空
    private BigDecimal validateBigDecimalField(List<Object> line, int index, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        if (value == null) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            lineErrors.add("row" + row + "：" + value + "(format error)");
            return null;
        }
    }


    //校验邮编分区发件邮编起始-结束区间与收件邮编起始-结束区间是否重复
    private String validateWeightField(List<Object> line, int index, int index1, int index2, int index3, int index4,Long regionId, String errorMessage, int row, List<String> lineErrors) {
        Object value = line.get(index);
        Object value1 = line.get(index1);
        Object value2 = line.get(index2);
        Object value3 = line.get(index3);
        Object value4 = line.get(index4);

        List<TmsServiceRegionDetailEntity> weightEntities = tmsServiceRegionDetailMapper.selectList(new LambdaQueryWrapper<TmsServiceRegionDetailEntity>()
                //.eq(TmsServiceRegionDetailEntity::getPostalName, value)
                .eq(TmsServiceRegionDetailEntity::getPostalCodeStart, value3)
                .eq(TmsServiceRegionDetailEntity::getPostalCodeEnd, value4)
                .eq(TmsServiceRegionDetailEntity::getShipperPostalCodeStart, value1)
                .eq(TmsServiceRegionDetailEntity::getShipperPostalCodeEnd, value2)
                .eq(TmsServiceRegionDetailEntity::getRegionId,regionId)
                .last("limit 1"));
        if (CollUtil.isNotEmpty(weightEntities)) {
            lineErrors.add("row" + row + "：" + errorMessage);
            return null;
        }
        return value.toString().trim();
    }

    //处理批量导入邮编明细
    public Integer processWeights(List<TmsServiceRegionDetailEntity> weightList) {
        if (CollUtil.isEmpty(weightList)) {
            return 0;
        }
        boolean saved = this.saveBatch(weightList, 500); // 批量写入，MyBatis Plus
        return saved ? weightList.size() : 0;
    }



}