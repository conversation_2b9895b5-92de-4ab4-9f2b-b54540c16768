package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsReportManagementDto;
import com.jygjexp.jynx.tms.dto.TmsRoutePlanReportDto;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.enums.TaskType;
import com.jygjexp.jynx.tms.mapper.TmsCustomerOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsLmdDriverMapper;
import com.jygjexp.jynx.tms.mapper.TmsReportAndOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsReportManagementMapper;
import com.jygjexp.jynx.tms.service.TmsReportManagementService;
import com.jygjexp.jynx.tms.vo.TmsCustomerOrderPageVo;
import com.jygjexp.jynx.tms.vo.TmsRoutePlanReportVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 中大件揽收派送报告
 *
 * <AUTHOR>
 * @date 2025-06-05 11:47:18
 */
@Service
@RequiredArgsConstructor
public class TmsReportManagementServiceImpl extends ServiceImpl<TmsReportManagementMapper, TmsReportManagementEntity> implements TmsReportManagementService {
    private final TmsReportManagementMapper reportManagementMapper;
    private  final TmsReportAndOrderMapper reportAndOrderMapper;
    private final TmsCustomerOrderMapper customerOrderMapper;
    private final TmsLmdDriverMapper driverMapper;

    // 生成报告
    @Override
    public Boolean saveReportManagement(TmsReportManagementEntity tmsReportManagementEntity) {
        // 新增报告
        boolean reportSaved = save(tmsReportManagementEntity);
        return reportSaved;
    }

    // 查询路径规划报告
    @Override
    public Page<TmsRoutePlanReportVo> listRoutePlans(Page page, TmsRoutePlanReportVo vo) {
        MPJLambdaWrapper<TmsReportManagementEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsReportManagementEntity.class)
                .selectAs(TmsLmdDriverEntity::getDriverId, TmsRoutePlanReportVo.Fields.driverId)
                .selectAs(TmsLmdDriverEntity::getDriverName, TmsRoutePlanReportVo.Fields.driverName)
                .selectAs(TmsLmdDriverEntity::getDriverNum, TmsRoutePlanReportVo.Fields.driverNo)
                .leftJoin(TmsReportAndOrderEntity.class, TmsReportAndOrderEntity::getReportOrderNo, TmsReportManagementEntity::getReportOrderNo)
                .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getTaskOrderNo, TmsReportAndOrderEntity::getBatchNo)
                .leftJoin(TmsLmdDriverEntity.class, TmsLmdDriverEntity::getDriverId, TmsTransportTaskOrderEntity::getDriverId)
                .eq(TmsReportManagementEntity::getReportType, 2)
                .eq(TmsTransportTaskOrderEntity::getTaskType, TaskType.DELIVERY.getCode())
                .like(StrUtil.isNotBlank(vo.getDriverName()), TmsLmdDriverEntity::getDriverName, vo.getDriverName())
                .like(StrUtil.isNotBlank(vo.getDriverNo()), TmsLmdDriverEntity::getDriverNum, vo.getDriverNo())
                .between(StrUtil.isNotBlank(vo.getStartTime())&&StrUtil.isNotBlank(vo.getEndTime()), TmsReportManagementEntity::getCreateTime, vo.getStartTime(), vo.getEndTime())
                .orderByDesc(TmsReportManagementEntity::getCreateTime)
                .distinct()
        ;
        return reportManagementMapper.selectJoinPage(page, TmsRoutePlanReportVo.class, wrapper);
    }

    // 查询报告详情
    @Override
    public R getDetailByReportNo(String reportNo, Integer type) {
        // 构造基础查询（查子单、目的地、收件人等字段）
        MPJLambdaWrapper<TmsReportAndOrderEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsReportAndOrderEntity.class)
                .selectAs(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsRoutePlanReportDto.Fields.entrustedOrderNumber)
                .selectAs(TmsOverAreaEntity::getName, TmsRoutePlanReportDto.Fields.overAreaName)
                .selectAs(TmsCustomerOrderEntity::getRouteNumber, TmsRoutePlanReportDto.Fields.routeNumber)
                .selectAs(TmsCustomerOrderEntity::getReceiverName, TmsRoutePlanReportDto.Fields.receiverName)
                .selectAs(TmsCustomerOrderEntity::getReceiverPhone, TmsRoutePlanReportDto.Fields.receiverPhone)
                .selectAs(TmsCustomerOrderEntity::getDestAddress, TmsRoutePlanReportDto.Fields.destAddress)
                .selectAs(TmsCustomerOrderEntity::getDestPostalCode, TmsRoutePlanReportDto.Fields.destPostalCode)
                .selectAs(TmsCustomerOrderEntity::getOrderStatus, TmsRoutePlanReportDto.Fields.orderStatus)
                .leftJoin(TmsCustomerOrderEntity.class, TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsReportAndOrderEntity::getOrderNo)
                .leftJoin(TmsOverAreaEntity.class, TmsOverAreaEntity::getRouteNumber, TmsCustomerOrderEntity::getRouteNumber)
                .eq(TmsReportAndOrderEntity::getOrderType, type)
                .eq(TmsReportAndOrderEntity::getReportOrderNo, reportNo);

        // 执行查询，获取子单相关信息（不含司机号）
        List<TmsRoutePlanReportDto> dtoList = reportAndOrderMapper.selectJoinList(TmsRoutePlanReportDto.class, wrapper);

        if (CollUtil.isEmpty(dtoList)) {
            return R.ok(Collections.emptyList());
        }

        String firstOrderNo = dtoList.get(0).getEntrustedOrderNumber();
        if (StrUtil.isBlank(firstOrderNo)) {
            return R.ok(Collections.emptyList());
        }

        // 从子单号中提取主单号（仅当长度大于15时才去掉后3位）
        Set<String> mainOrderNos = dtoList.stream()
                .map(dto -> {
                    String subOrderNo = dto.getEntrustedOrderNumber();
                    if (StrUtil.isNotBlank(subOrderNo) && subOrderNo.length() > 15) {
                        return subOrderNo.substring(0, subOrderNo.length() - 3);
                    } else {
                        return subOrderNo;
                    }
                })
                .filter(StrUtil::isNotBlank) // 保证非空
                .collect(Collectors.toSet());

        if (mainOrderNos.isEmpty()) {
            return R.ok(dtoList); // 子单没提取出主单号，直接返回原数据
        }

        // 查询主单记录，获取主单的 driverId
        List<TmsCustomerOrderEntity> mainOrders = customerOrderMapper.selectList(
                new LambdaQueryWrapper<TmsCustomerOrderEntity>()
                        .in(TmsCustomerOrderEntity::getEntrustedOrderNumber, mainOrderNos)
        );

        Map<String, Long> mainOrderDriverIdMap = mainOrders.stream()
                .filter(order -> StrUtil.isNotBlank(order.getEntrustedOrderNumber()) && order.getDeliveryDriverId() != null)
                .collect(Collectors.toMap(
                        TmsCustomerOrderEntity::getEntrustedOrderNumber,
                        TmsCustomerOrderEntity::getDeliveryDriverId
                ));

        // 批量查司机信息（driverId → driverNum）
        Set<Long> driverIds = new HashSet<>(mainOrderDriverIdMap.values());

        List<TmsLmdDriverEntity> drivers = Collections.emptyList();
        if (CollUtil.isNotEmpty(driverIds)) {
            drivers = driverMapper.selectList(
                    new LambdaQueryWrapper<TmsLmdDriverEntity>()
                            .in(TmsLmdDriverEntity::getDriverId, driverIds)
            );
        }

        Map<Long, String> driverIdToNumMap = drivers.stream()
                .filter(driver -> driver.getDriverId() != null && StrUtil.isNotBlank(driver.getDriverNum()))
                .collect(Collectors.toMap(
                        TmsLmdDriverEntity::getDriverId,
                        TmsLmdDriverEntity::getDriverNum
                ));

        // 最终结果回填到 DTO 中
        for (TmsRoutePlanReportDto dto : dtoList) {
            String subOrderNo = dto.getEntrustedOrderNumber();
            if (StrUtil.isBlank(subOrderNo)) {
                continue;
            }

            String mainOrderNo;
            if (subOrderNo.length() > 15) {
                mainOrderNo = subOrderNo.substring(0, subOrderNo.length() - 3);
            } else if (subOrderNo.length() >= 13) {
                mainOrderNo = subOrderNo.substring(0, 13);
            } else {
                mainOrderNo = subOrderNo;
            }

            Long driverId = mainOrderDriverIdMap.get(mainOrderNo);
            if (driverId != null) {
                String driverNum = driverIdToNumMap.get(driverId);
                if (driverNum != null) {
                    dto.setDriverNo(driverNum);
                }
            }
        }

        return R.ok(dtoList);
    }



    /**
     * 查询跟踪单详情列表(未扫/已扫明细)
     * @param isScan 是否扫描：0：未扫描/1:已扫描
     * @return
     */
//    @Override
//    public R listEntrustedOrder(Boolean isScan, Long id) {
//        MPJLambdaWrapper<TmsReportManagementEntity> wrapper = new MPJLambdaWrapper<>();
//        wrapper.selectAll(TmsReportManagementEntity.class)
//
//                .selectAs(TmsCustomerOrderEntity::getEntrustedOrderNumber, TmsReportManagementDto.Fields.entrustedOrderNumber)
//                .selectAs(TmsCustomerOrderEntity::getCustomerOrderNumber, TmsReportManagementDto.Fields.customerOrderNumber)
//                .selectAs(TmsCustomerOrderEntity::getOrderType, TmsReportManagementDto.Fields.orderType)
//                .selectAs(TmsCustomerOrderEntity::getCargoType, TmsReportManagementDto.Fields.cargoType)
//                .selectAs(TmsCustomerOrderEntity::getBusinessModel, TmsReportManagementDto.Fields.businessModel)
//                .selectAs(TmsCustomerOrderEntity::getReceiveType, TmsReportManagementDto.Fields.receiveType)
//                .selectAs(TmsCustomerEntity::getCustomerName, TmsReportManagementDto.Fields.entrustedCustomer)
//                .eq(TmsCustomerOrderEntity::getIsScan, isScan)
//                .eq(TmsReportManagementEntity::getId, id)
//                .leftJoin(TmsTransportTaskOrderEntity.class, TmsTransportTaskOrderEntity::getEntrustedOrderNumber, TmsReportManagementEntity::getEntrustedOrderNo)
//                .leftJoin(TmsCustomerEntity.class, TmsCustomerEntity::getId, TmsCustomerOrderEntity::getCustomerId)
//                .orderByDesc(TmsReportManagementEntity::getCreateTime);
//        List<TmsReportManagementDto> tmsReportManagementDtos = reportManagementMapper.selectJoinList(TmsReportManagementDto.class, wrapper);
//        return R.ok(tmsReportManagementDtos);
//    }

}