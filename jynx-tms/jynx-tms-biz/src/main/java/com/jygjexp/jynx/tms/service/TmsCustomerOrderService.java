package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.entity.TmsCustomerOrderEntity;
import com.jygjexp.jynx.tms.model.bo.ApiOrder;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerOrderExcelVo;
import com.jygjexp.jynx.tms.vo.excel.TmsCustomerZdjOrderExcelVo;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TmsCustomerOrderService extends IService<TmsCustomerOrderEntity> {

    //客户订单分页查询
    Page<TmsCustomerOrderPageVo> search(Page page, TmsCustomerOrderPageVo orderPageVo,Boolean KpFlag);

    String generalNewOrderNo(Long customerId, Integer businessModel);

    // 卡派订单询价
    R getOrderPrice(TmsEntrustedOrderVo vo);

    //订单调度-分配承运商-同步生成委托订单
    R assignCarrier(String customerOrderNumber, Long carrierId);

    //新建客户订单
    R create(TmsCustomerOrderEntity tmsEntrustedOrder);

    R createZDJOrder(TmsCustomerOrderEntity customerOrderEntity,Boolean isApi);

    // 推送商家客户端导入订单至小包
    R syncCreateOrder(List<TmsCustomerOrderEntity> customerOrderEntity);

    // 异常订单推送小包
    R exOrderPushXb(List<String> subOrderNo);

    // 订单调度-判断承运商区域(经纬度)
    Boolean isCarrierArea(TmsIsCarrierAreaVo vo);


    // 订单调度-判断承运商区域(三字邮编)
    Boolean isPostCarrierArea(TmsIsCarrierAreaVo vo);

    String getLatLngByAddress(String address, String region, String postalCode);

    GeocodeResult getLatLngByAddress(String address, String postalCode);

    // 取消下单
    Boolean cancelOrder(Long id);

    // 卡派客户订单API下单接口
    R createKpApiOrder(ApiOrder apiOrder);


    // 中大件客户订单API下单接口
    R createZdjApiOrder(ApiOrder apiOrder);

     // 获取客户订单信息
     TmsCustomerOrderEntity getByCustomerOrderNumber(String customerOrderNumber,Boolean isReferenceNumber);

     //删除订单
    R deleteOrder(String orderNo);

    //订单拦截
    R blockOrder(String orderNo);

    //查询订单是否被拦截
    Boolean isBlock(String orderNo);

    //获取轨迹接口
    R track(String orderNo);

    // 卡派客户订单导出
    List<TmsCustomerOrderExcelVo> getExcel(TmsCustomerOrderPageVo vo, Long[] ids);


   // 中大件客户订单导出
    List<TmsCustomerZdjOrderExcelVo> getZdjExcel(TmsCustomerOrderPageVo vo, Long[] ids);

    //修改客户订单
    R updateCustomerOrder(TmsCustomerOrderEntity tmsCustomerOrder);

    //修改中大件客户订单
    R updateCustomerOrderZdj(TmsCustomerOrderEntity tmsCustomerOrder);

    //修改卡派客户订单
    R updateZdjCustomerOrder(TmsCustomerOrderEntity tmsCustomerOrder);

    // 客户端查询订单列表
    Page<TmsCustomerOrderPageVo> clientList(Page page,TmsCustomerOrderPageVo vo);

    // ----------------------------------------------------------------------------------------中大件轨迹查询-------------------------------------------
    // 客户端查询轨迹列表  --废
    R getTrack(String orderNo);

    // 根据箱号查询对应轨迹节点记录  --废
    R getTrackList(String subOrderNo);

    // 中大件-轨迹查询
    R getZdjTrack(TmsOrderNoTrackVo orderNos);

    // 中大件-根据箱号查询对应轨迹节点记录
    R getZdjTrackList(String subOrderNo);

    // 官网中大件-轨迹查询新
    R getZdjWebTrackNew(TmsWebOrderTrackVo vo);

    // 中大件-客户端轨迹查询
    R getZdjClientTrack(TmsOrderNoTrackVo orderNos);

    // 中大件客户端-根据箱号查询对应轨迹节点记录
    R getZdjClientTrackList(String subOrderNo);

    // 订单详情箱号查询轨迹
    List<TmsOrderTrackVo> getOrderTrackList(String orderNo);

    // 客户端订单详情箱号查询轨迹
    List<TmsOrderTrackVo> getClientBoxTrack(String orderNo);

    // ----------------------------------------------------------------------------------------中大件轨迹查询-------------------------------------------

    Page<TmsCustomerOrderPageVo> clientSearch(Page page, TmsCustomerOrderPageVo orderPageVo,Boolean kpFlag);

    List<TmsCustomerOrderExcelVo> getClientExcel(TmsCustomerOrderPageVo vo, Long[] ids);

    //根据跟踪单号获取子单号
    List<TmsCustomerOrderEntity> getSubOrderNoByTrackNo(String trackNo);

    //批量删除客户订单
    R updateCustomerOrderByIds(Long[] ids);

    // 揽收指派列表(排除送货到仓且发货地三字邮编对应一级仓情况，不可揽收)
    Page<TmsCustomerOrderPageVo> listCollectionAssign(Page page, TmsCustomerOrderPageVo vo);

    // 可揽收客户订单列表
    Page<TmsCustomerOrderPageVo> getAllPickupCustomerOrder(Page page, TmsCustomerOrderPageVo vo);

    // 揽收指派列表详情
    Page<TmsCollectionAssignDetailPageVo> listCollectionAssignDetail(Page page, TmsCollectionAssignDetailPageVo vo);

    //查询最近经纬度为空的订单
    void processNoLatestOrder();

    //换单补充
    void supplementExchangeOrderInfo();
    //查询订单对应的司机信息(揽收、任务、干线)
    Map<String,List<TmsLmdDriverPageVo>> getDriverInfo(TmsCustomerOrderEntity tmsCustomerOrder);

    //获取订单详情
    TmsCustomerOrderPageVo getDetail(Long id);

    // 获取主子单pod详情
    List<PodVoBySub> getSubPod(String orderNo);

    //导入Excel
    R processFile(MultipartFile file,Long customerId) throws IOException;

    //根据子单号获取订单信息(主要用于面单)
    TmsCustomerOrderEntity getCustomerOrderBySubOrder(String trackingNo);

    //换单
    R exchangeOrder(ExchangeVo vo);

    //通过客户单号获取子单号
    TmsCustomerOrderEntity getByOrderByCustomerOrderNumber(String customerOrderNumber);

    //通过客户单号或者委托单号查询订单
    TmsCustomerOrderEntity getByOrderNumber(String orderNumber);

    //更新换单
    R updateExchangeOrder(ExchangeVo vo);


    R updatePod(String orderNo,String picUrl);

    // 面单打印校验
    R labelCheckout(@RequestParam String orderNo);

    TmsCustomerOrderEntity getCustomerOrderByOrderNo(String orderNo);


    //查询最近一个小时的订单合并面单合并PDF
    void getOrderByJobCondition();

    // 根据订单号获取订单日志
    R getOrderLog(String orderNo);

    R sortingUpdate(String orderNo);
    /**
     * 分拣机 - 轨迹跟踪 & 订单状态 WAREHOUSE_RECEIPT 11
     * @param entrustedOrderNumbers
     */
    void sortingOperated(List<String> entrustedOrderNumbers);
    // 官网中大件轨迹查询-校验验证码
//    R getCaptcha(List<String> orderList);
//
//    // 官网中大件轨迹校验验证码
//    R getCaptcha(String inputCode, List<String> orderList);

    List<TmsCustomerOrderEntity> getCustomerOrderByMain(String orderNo);

    //获取订单POD信息
    PodVo getCustomerOrderPod(String trackingNo);

     //获取子订单POD信息
    List<SubOrderPodVo> getCustomerSubOrderPod(String trackingNo);

    //添加复核体积和重量
    boolean addReviewVolumeAndWeight(BigDecimal volume, BigDecimal weight, String orderNo);
}
