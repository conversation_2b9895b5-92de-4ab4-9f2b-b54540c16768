package com.jygjexp.jynx.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jygjexp.jynx.common.data.entity.BaseLogicEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 手工调账
 *
 * <AUTHOR>
 * @date 2025-07-30 03:25:12
 */
@Data
@TableName("tms_store_balance_offline_recharge")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "手工调账")
public class TmsStoreBalanceOfflineRecharge extends BaseLogicEntity<TmsStoreBalanceOfflineRecharge> {


    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 门店客户id
     */
    @Schema(description = "门店id")
    private Long storeId;

    /**
     * 门店客户id
     */
    @Schema(description = "门店客户id")
    private Long storeCustomerId;

    /**
     * 消费记录id
     */
    @Schema(description = "消费记录id")
    private Long storeBalanceRecordId;

    /**
     * 支付方式
     */
    @NotNull(message = "支付方式不能为空")
    @Schema(description = "支付方式 1 银行转账 2 EMT支付")
    private Integer paymentMethod;

    /**
     * 付款信息
     */
    @NotBlank(message = "付款信息不能为空")
    @Schema(description = "付款信息")
    private String paymentInfo;

    /**
     * 付款账号
     */
    @NotBlank(message = "付款账号不能为空")
    @Schema(description = "付款账号")
    private String paymentAccount;

    /**
     * 付款方
     */
    @NotBlank(message = "付款方不能为空")
    @Schema(description = "付款方")
    private String payer;

    /**
     * 收款账号
     */
    @NotBlank(message = "收款账号不能为空")
    @Schema(description = "收款账号")
    private String receiveAccount;

    /**
     * 收款方
     */
    @NotBlank(message = "收款方不能为空")
    @Schema(description = "收款方")
    private String payee;

    /**
     * 金额
     */
    @DecimalMin(value = "0.01", message = "金额不能少于0.01")
    @NotNull(message = "金额不能为空")
    @Schema(description = "金额")
    private BigDecimal amount;

    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空")
    @Schema(description = "币种")
    private String currency;

    /**
     * 交易日期
     */
    @NotNull(message = "交易日期不能为空")
    @Schema(description = "交易日期")
    private LocalDate transactionDate;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String notes;

    /**
     * 审批人
     */
    @Schema(description = "审批人")
    private String verifyer;

    /**
     * 审批时间
     */
    @Schema(description = "审批时间")
    private LocalDateTime verifyTime;

    /**
     * 审批备注
     */
    @Schema(description = "审批备注")
    private String verifyRemarks;

    /**
     * 状态
     */
    @Schema(description = "状态 0:待审核;1:已审核;2:已驳回")
    private Integer status;

    /**
     * 客户名称
     */
    @TableField(exist = false)
    @Schema(description = "客户名称")
    private String customerName;

    /**
     * 门店名称
     */
    @TableField(exist = false)
    @Schema(description = "门店名称")
    private String storeName;
}