package com.jygjexp.jynx.basic.back.task;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jygjexp.jynx.basic.back.constants.TkzjConstants;
import com.jygjexp.jynx.basic.back.entity.PickupEntity;
import com.jygjexp.jynx.basic.back.model.vo.excel.ZtPickupExcelVo;
import com.jygjexp.jynx.basic.back.mapper.PickupMapper;
import com.jygjexp.jynx.basic.back.tools.AliYunOSS;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import javax.activation.DataHandler;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;

/**
 * @Author: chenchang
 * @Description: 预约单定时发送Excel邮件(取件日当天或上个取件日有司机待取件的单)
 * @Date: 2025/1/6 17:10
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SchedulePickupTask {
    private final PickupMapper pickupMapper;

    // 设置文件存储路径
//    private final static String directoryPath = "D:/exports";
    private final static String directoryPath = TkzjConstants.UNI_SIGN_FILE_PATH + "/pickup";

    @SneakyThrows
    @XxlJob("schedulePickupHandler")
    public void SchedulePickupHandler() {
        XxlJobHelper.log("定时任务：【预约单定时发送Excel邮件】于:{}，输入参数{}", LocalDateTime.now(), "运行中");

        runSchedulePickupTask();

        XxlJobHelper.handleSuccess(); // 设置任务结果
        XxlJobHelper.log("定时任务：【订单检查UNI派送】执行结束，时间: {}", LocalDateTime.now());
    }

    private void runSchedulePickupTask() {
        try {
            // 获取当前日期并格式化为 yyyy-MM-dd
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = dateFormat.format(new Date());

            // 使用当前日期筛选 pickDate 等于今天的记录 或取件日前一天有更新取件时段的记录
            List<PickupEntity> pickupList = pickupMapper.selectPickupList(formattedDate);
//            List<PickupEntity> pickupList = pickupMapper.selectList(new LambdaQueryWrapper<PickupEntity>().last("limit 100"));

            // 如果没有符合条件的记录，直接返回
            if (pickupList.isEmpty()) {
                log.info("没有符合条件的记录，退出导出");
                return;
            }

            List<ZtPickupExcelVo> dataList = Lists.newArrayList();
            for (PickupEntity ztPickup : pickupList) {
                ZtPickupExcelVo excelVo = new ZtPickupExcelVo();
                excelVo.setCustomerName(ztPickup.getLastName() + " " + ztPickup.getMiddleName() + " " + ztPickup.getFirstName());
                excelVo.setPhone(ztPickup.getPhone());
                excelVo.setExt(ztPickup.getExt());
                excelVo.setStreetAddress(ztPickup.getStreetAddress());
//                excelVo.setEmail(ztPickup.getEmail());
                excelVo.setStateName(ztPickup.getStateName());
                excelVo.setCityName(ztPickup.getCityName());
                excelVo.setZipCode(ztPickup.getZipCode());
                excelVo.setAptSuiteOther(ztPickup.getAptSuiteOther());
//                excelVo.setCreateDate(ztPickup.getCreateDate());
//                excelVo.setUpdateDate(ztPickup.getUpdateDate());
                excelVo.setConfirmationNumber(ztPickup.getConfirmationNumber());
                switch (ztPickup.getPackagesLocation()) {
                    case 1:
                        excelVo.setPackagesLocation("In/At Mailbox");
                        break;
                    case 2:
                        excelVo.setPackagesLocation("On the Porch");
                        break;
                    case 3:
                        excelVo.setPackagesLocation("Front Door");
                        break;
                    case 4:
                        excelVo.setPackagesLocation("Side DoorKnock on Door/Ring BellMail Room");
                        break;
                    case 5:
                        excelVo.setPackagesLocation("Office");
                        break;
                    case 6:
                        excelVo.setPackagesLocation("Reception");
                        break;
                    case 7:
                        excelVo.setPackagesLocation("Other(additional instructions required)");
                        break;
                    default:
                        excelVo.setPackagesLocation("");
                        break;
                }
                excelVo.setAdditionalInstructions(ztPickup.getAdditionalInstructions());
                excelVo.setPickDate(ztPickup.getPickDate());
                excelVo.setTotalWeight(ztPickup.getTotalWeight());
                switch (ztPickup.getArbitraryTime()) {
                    case 1:
                        excelVo.setArbitraryTime("9:00 a.m to 1:00 p.m");
                        break;
                    case 2:
                        excelVo.setArbitraryTime("1:00 p.m to 5:00 p.m");
                        break;
                    default:
                        excelVo.setArbitraryTime("");
                        break;
                }
                excelVo.setContraband(ztPickup.getIsContraband() == 0 ? "Not included" : "contraband");
                excelVo.setBags(ztPickup.getBags());
                excelVo.setBoxes(ztPickup.getBoxes());
                excelVo.setTrays(ztPickup.getTrays());
                excelVo.setPickTimeZone(ztPickup.getPickTimeZone());

                dataList.add(excelVo);
            }

            // 直接将数据写入到输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, ZtPickupExcelVo.class).sheet("数据导出").doWrite(dataList);
            byte[] excelBytes = outputStream.toByteArray(); // 获取 Excel 数据的字节数组

            // 上传到阿里云 OSS
            String fileName = "zt_pickup_export_" + formattedDate + ".xlsx";
            String ossFileUrl = AliYunOSS.sendToOssFromByteArray(excelBytes, "pickup_exports", fileName);
            log.info("文件已上传到 OSS，访问地址：" + ossFileUrl);

            // 调用邮件发送方法
//            String recipientEmail = "<EMAIL>"; // 收件人邮箱
            List<String> recipientEmails = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");
            String subject = "Export reservation form data";
            String content = "Please find attached the latest reservation data export.";

            // 发送带附件的邮件
            sendWithAttachment(recipientEmails, subject, content, ossFileUrl);
            log.info("附件邮件发送成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("导出或发送邮件时发生错误！");
        }
    }

    // 发送附近邮件
    private static void sendWithAttachment(List<String> emails, String subject, String content, String filePath) {
        Long startTime = System.currentTimeMillis();
        String host = "smtp.larksuite.com";
        final String user = "<EMAIL>";
        final String password = "paXpbkG8zG2R0ODB";

        Properties props = new Properties();
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.socketFactory.port", "465");
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", "465");

        Session session = Session.getDefaultInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(user, password);
            }
        });

        try {
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(user));
            for (String email : emails) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(email));
            }
            message.setSubject(subject);

            // 创建邮件正文部分
            MimeBodyPart textPart = new MimeBodyPart();
            textPart.setText(content);

            // 获取文件流
            InputStream fileInputStream = AliYunOSS.getFileStreamFromOss(filePath);
            // 将文件流读取到字节数组
            byte[] fileBytes = toByteArray(fileInputStream);

            // 创建邮件附件部分
            MimeBodyPart attachmentPart = new MimeBodyPart();
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            fileName = fileName.replace("oss-us-east-1.aliyuncs.com/", "");  // 去掉域名部分
            attachmentPart.setFileName(fileName); // 获取 OSS 文件名
            attachmentPart.setDataHandler(new DataHandler(new ByteArrayDataSource(fileBytes, "application/octet-stream")));

            // 将正文和附件组合到多部分消息中
            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(textPart);
            multipart.addBodyPart(attachmentPart);

            // 设置邮件内容为多部分内容
            message.setContent(multipart);

            // 发送邮件
            Transport.send(message);

            // 输出日志
            Long endTime = System.currentTimeMillis();
            log.info("邮件任务执行结束，耗时：" + (endTime - startTime) + "ms");
            log.info("邮件发送成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("邮件发送失败！", e);
        }
    }

    // 将 InputStream 转换为 byte[]
    private static byte[] toByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        try {
            while ((length = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }
        } finally {
            // 这里不要关闭 inputStream，因为我们还需要读取完数据后再关闭
            // inputStream.close();
        }
        return byteArrayOutputStream.toByteArray();
    }

}
