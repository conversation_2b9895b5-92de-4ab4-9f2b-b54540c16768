package com.jygjexp.jynx.basic.back.model.bo;

import com.baomidou.mybatisplus.annotation.TableField;

import com.jygjexp.jynx.basic.back.annotation.FieldLength;
import com.jygjexp.jynx.basic.back.annotation.FieldNotNull;
import com.jygjexp.jynx.basic.back.annotation.PatternField;
import com.jygjexp.jynx.basic.back.annotation.Validation;
import com.jygjexp.jynx.basic.back.entity.SubOrderEntity;
import com.jygjexp.jynx.basic.back.entity.TmsAdditionalServicesEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @className ApiOrder
 * @description 订单  为了更好的接收数据及提示验证错误信息，将字段属性全定为String类型
 * @email <EMAIL>
 * @date 2020/11/30 20:24
 **/
@Data
@Validation
public class ApiOrder {

    @Schema(description = "渠道编码")
    @FieldNotNull(message = "渠道编码为必填项，不能为空！")
//    @PatternField(regexp = "^[A-Za-z0-9]*$", message = "渠道编码只能为数字或字母！")
    @FieldLength(maxLength = 32, message = "渠道编码最大字符长度不能超过32位！")
    private String channelCode;

    @Schema(description = "客户参考号")
    @FieldNotNull(message = "客户参考号为必填项，不能为空！")
    @FieldLength(maxLength = 32, message = "客户参考号最大字符长度不能超过32位！")
    private String referenceNo;

    @Schema(description = "运单号")
    @Size(max = 50, message = "运单号最大字符长度不能超过50位！")
    @FieldLength(maxLength = 50, message = "运单号最大字符长度不能超过50位！")
    private String trackingNo;

//    @Schema(description = "产品类型")
//    @FieldNotNull(message = "产品类型为必填项，不能为空！")
//    @PatternField(regexp = "^[123]$", message = "产品类型的值只能为1、2或3，（1:包裹，2：文件，3:袋子）！")
//    private Integer productType;

    @Schema(description = "产品名称")
    @FieldLength(maxLength = 200, message = "产品名称大字符长度不能超过200位！")
    private String productName;

    @Schema(description = "订单材积明细列表")
    @NotNull(message = "至少填写一条材积明细列表！")
    private List<SubOrderEntity> apiOrderVolumeList;

    @Schema(description = "附加服务")
    private List<TmsAdditionalServicesEntity> tmsAdditionalServicesList;

//    @TableField(exist = false)
//    @Schema(description= "预计重量（kg）")
//    @FieldNotNull(message = "预计重量不能为空！")
//    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "预计重量应大于0，小于10000，且最多保留三位小数！")
//    private String pweight;

//
//    @FieldNotNull(message = "重量不能为空！")
//    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "重量应大于0，小于10000，且最多保留三位小数！")
//    @Schema(description = "重量（kg）")
//    private BigDecimal weight;
//
//    @Schema(description = "长度(cm)")
//    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "长度应大于0，小于10000，且最多保留三位小数！")
//    private BigDecimal length;
//
//    @Schema(description = "宽度(cm)")
//    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "宽度应大于0，小于10000，且最多保留三位小数！")
//    private BigDecimal width;
//
//    @Schema(description = "高度(cm)")
//    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "高度应大于0，小于10000，且最多保留三位小数！")
//    private BigDecimal height;

    @Schema(description = "价格($)")
    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "价格应大于0，小于10000，且最多保留三位小数！")
    private BigDecimal price;

    @Schema(description = "下单时间")
    private LocalDateTime orderTime;

    @Schema(description = "箱号")
    private BigDecimal bagNum;

    @Schema(description = "件数")
    @FieldNotNull(message = "件数为必填项，不能为空！")
    @PatternField(regexp = "^[1-9]\\d{0,3}|10000$", message = "件数只能为1到10000的整数！")
    private Integer pieces;

    @Schema(description = "是否购买保险")
    @PatternField(regexp = "^[01]$", message = "是否购买保险的取值只能是0或1,(0:否，1:是)")
    private String insured;

    @Schema(description = "带电类型")
    @FieldLength(maxLength = 10, message = "带电类型最大字符长度不能超过10位！")
    private String batteryType;

    @Schema(description = "发货模式")
    @Range(min = 1, max = 4, message = "发货模式只能是1-4")
    @FieldNotNull(message = "发货模式为必填项，不能为空！")
    private Integer shipMode;

    @Schema(description = "收件人姓名")
    @FieldNotNull(message = "收件人姓名为必填项，不能为空！")
    @FieldLength(maxLength = 100, message = "收件人姓名最大字符长度不能超过100位！")
    private String consigneeName;

    @Schema(description = "收件人公司")
    @FieldLength(maxLength = 100, message = "收件人公司最大字符长度不能超过100位！")
    private String consigneeCompany;

    @Schema(description = "收件人国家")
    @FieldLength(maxLength = 100, message = "收件人国家为必填项，不能为空！")
    private String consigneeCountryCode;

    @Schema(description = "收件人州/省")
    @FieldNotNull(message = "收件人州/省为必填项，不能为空！")
    @FieldLength(maxLength = 100, message = "收件人州/省最大字符长度不能超过100位！")
    private String consigneeProvince;

    @Schema(description = "收件人城市")
    @FieldNotNull(message = "收件人城市为必填项，不能为空！")
    @FieldLength(maxLength = 100, message = "收件人城市最大字符长度不能超过100位！")
    private String consigneeCity;

    @Schema(description = "收件人区/县")
    @FieldLength(maxLength = 100, message = "收件人区/县最大字符长度不能超过100位！")
    private String consigneeDistrict;

    @Schema(description = "收件人街道/地址")
    @FieldLength(maxLength = 500, message = "收件人街道/地址最大字符长度不能超过500位")
    private String consigneeStreet;

    @Schema(description = "收件人门牌号")
    @Size(max = 100, message = "收件人门牌号最大字符长度不能超过100位！")
    private String consigneeHouseNumber;

    @Schema(description = "收件人地址")
    @FieldNotNull(message = "收件人地址为必填项不能为空！")
    @FieldLength(maxLength = 500, message = "收件人地址最大字符长度不能超过500位")
    private String consigneeAddress;//'收件人地址'

    @Schema(description = "收件人地址")
    private String consigneeAddress2;//'收件人地址'


    @Schema(description = "收件人邮编")
    @FieldNotNull(message = "收件人邮编为必填项，不能为空！")
    @FieldLength(maxLength = 32, message = "收件人邮编最大字符长度不能超过32位")
    private String consigneePostcode;//'收件人邮编'

    @Schema(description = "收件人手机")
    @FieldLength(maxLength = 32, message = "收件人手机最大字符长度不能超过32位")
    private String consigneeMobile;//'收件人手机'

    //    @NotBlank(message = "收件人电话为必填项，不能为空！")
//    @FieldNotNull(message = "收件人电话为必填项，不能为空！")
    @FieldLength(maxLength = 32, message = "收件人电话为必填项最大字符长度不能超过32位")
    private String consigneePhone;//'收件人电话'

    @Schema(description = "收件人邮箱")
    @FieldLength(maxLength = 100, message = "收件人邮箱最大字符长度不能超过100位！")
    private String consigneeEmail;//'收件人邮箱'

    @Schema(description = "收件人护照号")
    @FieldLength(maxLength = 100, message = "收件人护照号最大字符长度不能超过100位！")
    private String consigneePassport;//'收件人护照号'

    @Schema(description = "收件人仓库")
    @FieldLength(maxLength = 100, message = "收件人仓库最大字符长度不能超过100位！")
    private String consigneeWarehouse;//'收件人仓库'

    @Schema(description = "收件人税号")
    @FieldLength(maxLength = 100, message = "收件人税号最大字符长度不能超过100位！")
    private String consigneeTariff;//'收件人税号'

    @Schema(description = "落地口岸")
    @FieldLength(maxLength = 20, message = "落地口岸长度不能超过20位！")
    private String arrivePortName;//落地口岸

    @Schema(description = "发件人姓名")
    @FieldLength(maxLength = 100, message = "发件人姓名最大字符长度不能超过100位！")
    private String shipperName;//'发件人姓名'

    @Schema(description = "发件人公司")
    @FieldLength(maxLength = 100, message = "发件人公司最大字符长度不能超过100位！")
    private String shipperCompany;//'发件人公司'

    @Schema(description = "发件人国家")
//    @FieldNotNull(message = "发件人国家为必填项，不能为空！")
    private String shipperCountryCode;//'发件人国家'

    @Schema(description = "发件人省/州")
    @FieldLength(maxLength = 100, message = "发件人省/州最大字符长度不能超过100位！")
    private String shipperProvince;//'发件人省/州'

    @Schema(description = "发件人城市")
    @FieldLength(maxLength = 100, message = "发件人城市最大字符长度不能超过100位！")
    private String shipperCity;//'发件人城市'

    @Schema(description = "发件人区/县")
    @FieldLength(maxLength = 100, message = "发件人区/县最大字符长度不能超过100位！")
    private String shipperDistrict;//'发件人区/县'

    @Schema(description = "发件人街道")
    @FieldLength(maxLength = 200, message = "发件人街道最大字符长度不能超过200位！")
    private String shipperStreet;//'发件人街道'

    @Schema(description = "发件人门牌号")
    @FieldLength(maxLength = 100, message = "发件人门牌号最大字符长度不能超过100位！")
    private String shipperHouseNumber;//'发件人门牌号'

    @Schema(description = "发件人地址")
    @FieldNotNull(message = "发件人地址为必填项，不能为空！")
    @FieldLength(maxLength = 200, message = "发件人地址编最大字符长度不能超过200位！")
    private String shipperAddress;//'发件人地址'

    @Schema(description = "发件人邮编")
    @FieldNotNull(message = "发件人邮编为必填项，不能为空！")
    @FieldLength(maxLength = 32, message = "发件人邮编最大字符长度不能超过32位！")
    private String shipperPostcode;//'发件人邮编'

    @Schema(description = "发件人手机")
    @FieldLength(maxLength = 32, message = "发件人手机最大字符长度不能超过32位！")
    private String shipperMobile;//'发件人手机'

    @Schema(description = "发件人电话")
    @FieldNotNull(message = "发件人电话为必填项，不能为空！")
    @FieldLength(maxLength = 32, message = "发件人电话最大字符长度不能超过32位！")
    private String shipperPhone;//'发件人电话'

    @Schema(description = "发件人邮箱")
    @FieldLength(maxLength = 32, message = "发件人邮箱最大字符长度不能超过32位！")
    private String shipperEmail;//'发件人邮箱'

//    @TableField(exist = false)
//    @Schema(description= "货品总净重（小数）")
//    @Min(value = 0,message = "货品总净重必须大于0！")
//    private Double productWeightD;
//
//    @TableField(exist = false)
//    @Schema(description= "货品总价值（小数）")
//    @Min(value = 0,message = "货品总价值必须大于0！")
//    private Double productSumD;

    @Schema(description = "代收货款（小数）")
    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,2}|(0\\.\\d{1,2})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,2})|[1-9]{1,4}(\\.(\\d){1,2})?|10000|10000.0|10000.00)$", message = "代收货款金额应在0至10000之间，最多保留2位小数")
    private String codSum;

    @Schema(description = "代收货款币种")
//    @PatternField(regexp = "^CNY|USD|EUR|GBP|AUD|CAD|JPY|HKD$",message = "代收货款币种不正确")
    @PatternField(regexp = "^USD$", message = "代收货款币种暂只支持USD")
    private String codCurrencyCode;//'币种',CNY=人民币,USD美元,EUR=欧元,GBP=英镑,AUD=澳元,CAD=加元,JPY=日元,HKD=港币

    @Schema(description = "币种")
//    @PatternField(regexp = "^USD$",message = "申报币种暂只支持USD")
    private String currencyCode;//'币种',CNY=人民币,USD美元,EUR=欧元,GBP=英镑,AUD=澳元,CAD=加元,JPY=日元,HKD=港币

    @FieldLength(maxLength = 100, message = "备注最大字符长度不能超过100位！")
    private String memo;

    private Integer createUser;

    @Schema(description = "订单申报明细列表")
    private List<OrderItem> apiOrderItemList;


    @Schema(description = "预报时是否返回标签地址")
    private String returnLabel;

    private Integer copyData;


    @Schema(description = "驿站编码")
    @PatternField(regexp = "^[A-Za-z0-9]*$", message = "驿站编码只能为数字或字母！")
    @FieldLength(maxLength = 32, message = "驿站编码最大字符长度不能超过32位！")
    private String postalCode;

    @Schema(description = "退件仓")
    @FieldLength(maxLength = 32, message = "退件仓最大字符长度不能超过32位！")
    private String returnWarehouse;

    @Schema(description = "海外退件策略")
    @Range(min = 1, max = 2, message = "海外退件策略只能是1或2")
    private Integer overseasReturnStrategy;

    @Schema(description = "收货方式")
    @Range(min = 1, max = 2, message = "收货方式只能是1或2")
    @FieldNotNull(message = "收货方式不能为空!")
    private Integer receiveType;

    @Schema(description = "危险品编号类型")
    private String dgNoType;//UN,联合国危险货物编号是一组4位数字,NA，北美危险货物编号

    @Schema(description = "危险品编号")
    private String dgNo;

    @Schema(description = "危险品等级")
    private String dgPrimaryClass;

    @Schema(description = "危险品包装说明")
    private String dgPackingInstructions;

    @Schema(description = "危险品描述")
    private String dgProperShippingName;

    @Schema(description = "危险品计量")
    @PatternField(regexp = "^(?:[1-9][0-9]*\\.[0-9]+|0\\.(?!0+$)[0-9]+|[1-9]+\\d*)$", message = "危险品计量只能为正数，请正确填写")
    private String dgMeasure;

    @Schema(description = "危险品计量单位，kg/g  l/ml")
    private String dgMeasureUnit;

    @Schema(description = "可供装运的飞机类型")
    private transient String dgAircraftCategoryType;//PASSENGER_AND_CARGO_AIRCRAFT,CARGO_AIRCRAFT_ONLY

    @Schema(description = "erp名称")
    private String erpSource;

    @Schema(description = "报关币种")
    private String declarationCurrencyCode; //报关币种(国内)

    @PatternField(regexp = "^(YES|NO)$", message = "是否需要签名服务只能为YES或NO")
    private String signature;

    @Schema(description = "注入点ID")
    @FieldLength(maxLength = 32, message = "注入点ID字符长度不能超32位")
    private String fromAddressId;

    @PatternField(regexp = "^(LABEL|QRCODE)$", message = "返回面单类型只能是LABEL或QRCODE")
    private String labelType;


    // 预计发货时间开始-格式：yyyy-MM-dd 00:00:00
    //@FieldNotNull(message = "预计发货开始时间不能为空!")
    private LocalDateTime estimatedShippingTimeStart;

    // 预计发货时间结束-格式：yyyy-MM-dd 00:00:00
    //@FieldNotNull(message = "预计发货结束时间不能为空!")
    private LocalDateTime estimatedShippingTimeEnd;

    @Schema(description="预计到货时间开始-格式：yyyy-MM-dd 00:00:00")
    //@FieldNotNull(message = "预计到货开始时间不能为空!")
    private LocalDateTime estimatedArrivalTimeStart;

    @Schema(description="预计到货时间结束-格式：yyyy-MM-dd 00:00:00")
    //@FieldNotNull(message = "预计到货结束时间不能为空!")
    private LocalDateTime estimatedArrivalTimeEnd;


    @Schema(description="订单类型：1=托盘，2=包裹")
    @Range(min = 1, max = 2, message = "订单类型只能是1或2")
    @FieldNotNull(message = "订单类型不能为空!")
    private Integer orderType;

    @Schema(description="运输类型：1=整车运输，2=零担运输")
    @Range(min = 1, max = 2, message = "运输类型只能是1或2")
    @FieldNotNull(message = "运输类型不能为空!")
    private Integer transportType;

    @Schema(description="货物类型：1=普通货物，2=危险货物")
    @FieldNotNull(message = "货物类型不能为空!")
    @Range(min = 1, max = 2, message = "货物类型只能是1或2,1=普通货物，2=危险货物")
    private Integer cargoType;

    @Schema(description="地址类型：1=普通地址，2=商业地址")
    @Range(min = 1, max = 2, message = "地址类型只能是1-2")
    private Integer addressType;


    @Schema(description="总重量(kg)")
   // @FieldNotNull(message = "总重量不能为空！")
    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "总重量应大于0，小于10000，且最多保留三位小数！")
    private BigDecimal totalWeight;

    @Schema(description="总体积(m³)")
  //  @FieldNotNull(message = "总体积不能为空！")
    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "总体积应大于0，小于10000，且最多保留三位小数！")
    private BigDecimal totalVolume;

    @Schema(description="总运费")
   // @FieldNotNull(message = "总运费不能为空！")
    @PatternField(regexp = "^([1-9]\\d{1,3}\\.\\d{1,3}|(0\\.\\d{1,3})|([1-9]\\d{0,3})|([0]\\.[1-9]{1,3})|[1-9]{1,4}(\\.(\\d){1,3})?|10000|10000.0|10000.00|10000.000)$", message = "总运费应大于0，小于10000，且最多保留三位小数！")
    private BigDecimal totalFreight;

    @Schema(description="承运商编码")
//    @FieldNotNull(message = "承运商编码为必填项，不能为空！")
//    @FieldLength(maxLength = 32, message = "承运商编码最大字符长度不能超过32位！")
    private String carrierCode;

    @Schema(description="附加服务类型")
    private String additionalServiceType;

    @Schema(description="附加服务取值")
    private String additionalServiceValue;

    @Schema(description="附加服务时间")
    private LocalDateTime additionalServiceTime;

    @Schema(description="是否城市/郊区：0 郊区，1 城市")

    @Range(min = 1, max = 2, message = "是否城市/郊区：0 郊区，1 城市 必填")
    private Integer isUrban;

    //国家ID
    private Integer countryId;

    //省份ID
    private Integer provinceId;

    //城市ID
    private Integer cityId;

    //AuthId
    private Integer authId;

    //是否需要面单(0不需要；1需要，默认1)
    private Integer needLabel;





}
