package com.jygjexp.jynx.basic.back.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.basic.back.entity.SupportEntity;
import com.jygjexp.jynx.basic.back.mapper.SupportMapper;
import com.jygjexp.jynx.basic.back.model.bo.SupportBo;
import com.jygjexp.jynx.basic.back.model.vo.OldResult;
import com.jygjexp.jynx.basic.back.model.vo.excel.ZtSupportExcelVo;
import com.jygjexp.jynx.basic.back.service.SupportService;
import com.jygjexp.jynx.basic.back.tools.AliYunOSS;
import com.jygjexp.jynx.basic.back.tools.PickupMailSender;
import com.jygjexp.jynx.common.core.util.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.DataHandler;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.util.ByteArrayDataSource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: chenchang
 * @Description: 官网帮助页
 * @Date: 2025/5/9 15:51
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupportServiceImpl extends ServiceImpl<SupportMapper, SupportEntity> implements SupportService {
    private final SupportMapper supportMapper;

    // 保存Support反馈信息
    @Override
    public OldResult saveTicket(SupportBo bo) {
        Integer reasonSelect = bo.getReasonSelect();
        String details = bo.getDetails();
        String firstName = bo.getFirstName();
        String middleName = bo.getMiddleName();
        String lastName = bo.getLastName();
        String email = bo.getEmail();
        String trackingNumber = bo.getTrackingNumber();
        String country = bo.getCountry();
        String stateName = bo.getStateName();
        String location = bo.getLocation();
        String howToHelp = bo.getHowToHelp();
        String attachment = bo.getAttachment();

        SupportEntity support = new SupportEntity();
        String contactReason = "";
        if (null == reasonSelect || (!(reasonSelect > 0) && reasonSelect < 10)) {
            return OldResult.fail("-1", "The reason for contact can only be a number between 0-9");
        }
        switch (reasonSelect) {
            case 1:
                contactReason = "Tracking not updated";
                break;
            case 2:
                contactReason = "Parcel not scanned at drop-off point";
                break;
            case 3:
                contactReason = "Duplicate shipping label";
                break;
            case 4:
                contactReason = "Drop-off point too far / inaccessible";
                break;
            case 5:
                contactReason = "Drop-off point refused package";
                break;
            case 6:
                contactReason = "Damaged or missing item";
                break;
            case 7:
                contactReason = "Uncollected parcel overdue";
                break;
            case 8:
                contactReason = "Picked up but not received at warehouse";
                break;
            case 9:
                contactReason = "Other – please specify";
                break;
            default:
                contactReason = "";
                break;
        }
        support.setContactReason(contactReason);
        support.setDetails(details);
        support.setFirstName(firstName);
        support.setMiddleName(StrUtil.isBlank(middleName) ? " " : middleName);
        support.setLastName(lastName);
        support.setEmail(email);
        support.setTrackingNumber(trackingNumber);
        support.setCountry(country);
        support.setStateName(stateName);
        support.setLocation(location);
        support.setHowToHelp(howToHelp);
        support.setAttachment(attachment);
        support.setCreateDate(new Date());

        // 保存记录
        int insert = supportMapper.insert(support);
        if (insert <= 0) {
            return OldResult.fail("-1", "Failed to save the support information");
        }
        // 保存成功 发送反馈邮件
        toSendEmail(support);
        log.info("Successfully saved and sent feedback email：", support.getEmail());

        JSONObject jo = new JSONObject();
        jo.put("contactReason", support.getContactReason());
        jo.put("details", support.getDetails());
        jo.put("firstName", support.getFirstName());
        jo.put("middleName", support.getMiddleName());
        jo.put("lastName", support.getLastName());
        jo.put("email", support.getEmail());
        jo.put("trackingNumber", support.getTrackingNumber());
        jo.put("country", support.getCountry());
        jo.put("stateName", support.getStateName());
        jo.put("location", support.getLocation());
        jo.put("howToHelp", support.getHowToHelp());
        jo.put("attachment", support.getAttachment());
        jo.put("createDate", support.getCreateDate());
        return OldResult.ok("0", jo);
    }

    // 发送反馈邮件
    private void toSendEmail(SupportEntity support) {
        try {
            // 获取当前日期并格式化为 yyyy-MM-dd
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = dateFormat.format(new Date());

            List<ZtSupportExcelVo> dataList = new ArrayList<>();
            ZtSupportExcelVo excelVo = new ZtSupportExcelVo();
//        excelVo.setContactReason(support.getContactReason());
            BeanUtil.copyProperties(support, excelVo);
            dataList.add(excelVo);

            // 直接将数据写入到输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, ZtSupportExcelVo.class).sheet("数据导出").doWrite(dataList);
            byte[] excelBytes = outputStream.toByteArray(); // 获取 Excel 数据的字节数组

            // 上传到阿里云 OSS
            String fileName = "zt_support_export_" + formattedDate + ".xlsx";
            String ossFileUrl = AliYunOSS.sendToOssFromByteArray(excelBytes, "support_exports", fileName);
            log.info("文件已上传到 OSS，访问地址：" + ossFileUrl);

//            String email = "<EMAIL>";
            List<String> recipientEmails = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");
            String subject = "Customer Feedback from NB Official Website";
            String content = "Please find attached the latest reservation data export.";
            sendWithAttachment(recipientEmails, subject, content, ossFileUrl);
            log.info("附件邮件发送成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("导出或发送邮件时发生错误！");
        }

    }

    // 发送附近邮件
    private static void sendWithAttachment(List<String> emails, String subject, String content, String filePath) {
        Long startTime = System.currentTimeMillis();
        String host = "smtp.larksuite.com";
        final String user = "<EMAIL>";
        final String password = "paXpbkG8zG2R0ODB";

        Properties props = new Properties();
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.socketFactory.port", "465");
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", "465");

        Session session = Session.getDefaultInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(user, password);
            }
        });

        try {
            // 创建邮件消息
            MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(user));
            for (String email : emails) {
                message.addRecipient(Message.RecipientType.TO, new InternetAddress(email));
            }
            message.setSubject(subject);

            // 创建邮件正文部分
            MimeBodyPart textPart = new MimeBodyPart();
            textPart.setText(content);

            // 获取文件流
            InputStream fileInputStream = AliYunOSS.getFileStreamFromOss(filePath);
            // 将文件流读取到字节数组
            byte[] fileBytes = toByteArray(fileInputStream);

            // 创建邮件附件部分
            MimeBodyPart attachmentPart = new MimeBodyPart();
            String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
            fileName = fileName.replace("oss-us-east-1.aliyuncs.com/", "");  // 去掉域名部分
            attachmentPart.setFileName(fileName); // 获取 OSS 文件名
            attachmentPart.setDataHandler(new DataHandler(new ByteArrayDataSource(fileBytes, "application/octet-stream")));

            // 将正文和附件组合到多部分消息中
            Multipart multipart = new MimeMultipart();
            multipart.addBodyPart(textPart);
            multipart.addBodyPart(attachmentPart);

            // 设置邮件内容为多部分内容
            message.setContent(multipart);

            // 发送邮件
            Transport.send(message);

            // 输出日志
            Long endTime = System.currentTimeMillis();
            log.info("邮件任务执行结束，耗时：" + (endTime - startTime) + "ms");
            log.info("邮件发送成功！");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("邮件发送失败！", e);
        }
    }

    // 将 InputStream 转换为 byte[]
    private static byte[] toByteArray(InputStream inputStream) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        try {
            while ((length = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }
        } finally {
            // 这里不要关闭 inputStream，因为我们还需要读取完数据后再关闭
            // inputStream.close();
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 官网上传图片
     * @param file 文件流
     * @param dir 文件夹
     * @param groupId 分组ID
     * @param type 类型
     * @return
     */
    @Override
    public R uploadFile(MultipartFile file, String dir, Long groupId, String type) {
        String fileName = IdUtil.simpleUUID() + StrUtil.DOT + FileUtil.extName(file.getOriginalFilename());
        Map<String, String> resultMap = new HashMap<>(4);
        resultMap.put("bucketName", "nbexpress");
        resultMap.put("fileName", fileName);

        String fileUrl = null;
        try {
            // 调用 sendToOss 方法上传文件到 OSS 并获取文件 URL
            fileUrl = AliYunOSS.sendToOssTwo(file, dir, fileName);
            // 设置返回结果的 URL
            resultMap.put("url", fileUrl);
        } catch (Exception e) {
            log.error("上传失败", e);
            return R.failed(e.getLocalizedMessage());
        }
        return R.ok(resultMap);
    }

}
