<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.jygjexp</groupId>
		<artifactId>jynx-app-server</artifactId>
		<version>5.6.0</version>
	</parent>

	<artifactId>jynx-app-server-api</artifactId>

	<dependencies>
		<!--core 工具类-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-core</artifactId>
		</dependency>
		<!--mybatis plus extension,包含了mybatis plus core-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>
		<!--feign 工具类-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-feign</artifactId>
		</dependency>
		<!-- excel 导入导出 -->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-excel</artifactId>
		</dependency>
		<!-- 脱敏工具类-->
		<dependency>
			<groupId>com.jygjexp</groupId>
			<artifactId>jynx-common-sensitive</artifactId>
		</dependency>

		<!-- 日志相关		-->
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>aliyun-log-logback-appender</artifactId>
		</dependency>
	</dependencies>
</project>
