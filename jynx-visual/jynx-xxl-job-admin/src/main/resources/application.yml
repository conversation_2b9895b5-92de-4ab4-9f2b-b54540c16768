# 此配置只适合开发测试环境，详细配置参考： http://t.cn/A64RaHJm
server:
  port: 9080
  servlet:
    context-path: /xxl-job-admin

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: ${NACOS_HOST:jynx-register}:${NACOS_PORT:8848}
        metadata:
          management.context-path: ${server.servlet.context-path}/actuator
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
  config:
    import:
      - optional:nacos:<EMAIL>@.yml
      - optional:nacos:${spring.application.name}-@profiles.active@.yml
